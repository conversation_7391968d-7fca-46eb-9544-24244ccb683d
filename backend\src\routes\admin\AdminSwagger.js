const { swagger } = require('../../util/swagger');
const { Permissions, AdminRole } = require('../../../lib/models/enums');

const adminSchema = {
    type: 'object',
    properties: {
        email: {
            type: 'string',
            format: 'email',
            description: 'Admin email address'
        },
        password: {
            type: 'string',
            format: 'password',
            description: 'Admin password'
        },
        firstName: {
            type: 'string',
            description: 'Admin first name'
        },
        lastName: {
            type: 'string',
            description: 'Admin last name'
        },
        role: {
            type: 'string',
            enum: Object.values(AdminRole),
            description: 'Admin role'
        },
        permissions: {
            type: 'array',
            items: {
                type: 'string',
                enum: Object.values(Permissions)
            },
            description: 'List of permissions'
        },
        isActive: {
            type: 'boolean',
            default: true,
            description: 'Admin account status'
        }
    },
    required: ['email', 'password', 'firstName', 'lastName', 'role']
};

const adminSwagger = {
    tags: [
        {
            name: 'Admin',
            description: 'Admin management APIs'
        }
    ],
    paths: {
        '/api/admin/log-in': {
            post: {
                tags: ['Admin'],
                summary: 'Admin login',
                description: 'Authenticate admin user',
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    email: {
                                        type: 'string',
                                        format: 'email',
                                        example: '<EMAIL>'
                                    },
                                    password: {
                                        type: 'string',
                                        format: 'password',
                                        example: 'Qwerty12#'
                                    }
                                },
                                required: ['email', 'password']
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Login successful',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        token: {
                                            type: 'string',
                                            description: 'JWT access token'
                                        },
                                        admin: {
                                            $ref: '#/components/schemas/Admin'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/admin/profile': {
            get: {
                tags: ['Admin'],
                summary: 'Get admin profile',
                description: 'Get current admin user profile',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                responses: {
                    200: {
                        description: 'Profile retrieved successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Admin'
                                }
                            }
                        }
                    }
                }
            },
            put: {
                tags: ['Admin'],
                summary: 'Update admin profile',
                description: 'Update current admin user profile',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    firstName: {
                                        type: 'string',
                                        description: 'Admin first name'
                                    },
                                    lastName: {
                                        type: 'string',
                                        description: 'Admin last name'
                                    },
                                    currentPassword: {
                                        type: 'string',
                                        format: 'password',
                                        description: 'Current password for verification'
                                    },
                                    newPassword: {
                                        type: 'string',
                                        format: 'password',
                                        description: 'New password'
                                    }
                                }
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Profile updated successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Admin'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/admin/permissions': {
            get: {
                tags: ['Admin'],
                summary: 'Get all available permissions',
                description: 'Get list of all available permissions in the system',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                responses: {
                    200: {
                        description: 'Permissions retrieved successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        success: {
                                            type: 'boolean',
                                            example: true
                                        },
                                        data: {
                                            type: 'object',
                                            properties: {
                                                permissions: {
                                                    type: 'array',
                                                    items: {
                                                        type: 'string',
                                                        enum: Object.values(Permissions)
                                                    }
                                                }
                                            }
                                        },
                                        message: {
                                            type: 'string',
                                            example: 'Permissions retrieved successfully'
                                        }
                                    }
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Unauthorized',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Internal Server Error',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/admin/{id}/permissions': {
            put: {
                tags: ['Admin'],
                summary: 'Update admin permissions',
                description: 'Update permissions for a specific admin user',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        schema: { type: 'string', format: 'objectId' }
                    }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    permissions: {
                                        type: 'array',
                                        items: {
                                            type: 'string',
                                            enum: Object.values(Permissions)
                                        }
                                    }
                                },
                                required: ['permissions']
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Permissions updated successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Admin'
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    components: {
        schemas: {
            Admin: adminSchema
        }
    }
};

module.exports = adminSwagger;
