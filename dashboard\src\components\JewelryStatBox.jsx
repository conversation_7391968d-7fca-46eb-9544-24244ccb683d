import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { tokens } from '../theme';

const JewelryStatBox = ({ title, subtitle, icon, value, trend, trendValue }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const isPositiveTrend = trend === 'up';

  return (
    <Box
      sx={{
        backgroundColor: colors.whiteAccent[100],
        borderRadius: '12px',
        padding: { xs: '1rem', sm: '1.5rem' },
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        '&:hover': {
          transform: { xs: 'none', sm: 'translateY(-5px)' },
          boxShadow: { xs: '0 4px 20px rgba(0, 0, 0, 0.08)', sm: '0 8px 30px rgba(0, 0, 0, 0.12)' },
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '5px',
          height: '100%',
          backgroundColor: colors.primary[500],
        }
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
        }}
      >
        <Box>
          <Typography
            variant="h4"
            fontWeight="600"
            sx={{
              color: colors.primary[500],
              fontFamily: '"Playfair Display", serif',
              mb: '5px',
              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' }
            }}
          >
            {title}
          </Typography>

          <Typography
            variant="h2"
            fontWeight="bold"
            sx={{
              color: colors.blackAccent[700],
              fontFamily: '"Playfair Display", serif',
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem' }
            }}
          >
            {value}
          </Typography>
        </Box>

        <Box
          sx={{
            backgroundColor: colors.primary[100],
            borderRadius: '50%',
            p: { xs: '8px', sm: '10px' },
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {icon}
        </Box>
      </Box>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: { xs: '0.7rem', sm: '1rem' },
        }}
      >
        <Typography variant="body2" sx={{ color: colors.blackAccent[600] }}>
          {subtitle}
        </Typography>

        {trendValue && (
          <Typography
            variant="body2"
            sx={{
              color: isPositiveTrend ? colors.greenAccent[500] : colors.redAccent[500],
              display: 'flex',
              alignItems: 'center',
              fontWeight: 'bold',
            }}
          >
            {isPositiveTrend ? '↑' : '↓'} {trendValue}
          </Typography>
        )}
      </Box>

      <Box
        sx={{
          position: 'absolute',
          bottom: '-20px',
          right: '-20px',
          width: { xs: '80px', sm: '100px' },
          height: { xs: '80px', sm: '100px' },
          borderRadius: '50%',
          backgroundColor: colors.primary[100],
          opacity: 0.3,
          zIndex: 0,
        }}
      />
    </Box>
  );
};

export default JewelryStatBox;
