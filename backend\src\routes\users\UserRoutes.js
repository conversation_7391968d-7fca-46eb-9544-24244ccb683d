const express = require('express');
const router = express.Router();
const UserController = require('./UserController');
const validations = require('./UserValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');

router.get('/list', verifyToken, UserController.list);

router.get('/profile', verifyTokenUserOrAdmin, validate(validations.optionalId, 'query'), UserController.profile);

router.put('/profile', verifyTokenUserOrAdmin, validate(validations.updateProfile), UserController.updateProfile);

router.put('/password', verifyToken, validate(validations.updatePassword), UserController.updatePassword);

router.put('/email', verifyToken, validate(validations.updateEmail), UserController.updateEmail);

router.put('/phone', verifyToken, validate(validations.updatePhone), UserController.updatePhone);

router.get('/notification-toggle', verifyToken, UserController.notificationToggle);

router.post('/address', verifyTokenUserOrAdmin, validate(validations.addAddress), UserController.addAddress);

router.put('/address/:addressId', verifyTokenUserOrAdmin, validate(validations.addAddress), UserController.editAddress);

router.get('/address/:addressId', verifyTokenUserOrAdmin, UserController.getAddressDetail);

router.get('/address', verifyTokenUserOrAdmin, UserController.listAddresses);

module.exports = router;
