require('custom-env').env('api');
const mongoose = require('mongoose');
const AdminSettings = require('../../lib/models/models/AdminSettings.model');

async function updateAdminSettings() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        // Update existing admin settings
        const result = await AdminSettings.updateOne(
            {}, // empty filter to match any document
            {
                $set: {
                    webAppVersion: '1.0.0',
                    webForceUpdate: false
                }
            },
            { upsert: false }
        );

        if (result.modifiedCount > 0) {
            console.log('Admin settings updated successfully');
        } else {
            console.log('No admin settings found to update');
        }
    } catch (error) {
        console.error('Error updating admin settings:', error);
    } finally {
        await mongoose.disconnect();
    }
}

updateAdminSettings(); 