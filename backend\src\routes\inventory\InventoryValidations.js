const { Joi } = require('../../util/validations');
const Categories = require('../../../lib/models/enums/Categories.enum');

const requireSlug = Joi.object().keys({
    slug: Joi.string().required(),
});

const requireId = Joi.object().keys({
    id: Joi.string()
        .required()
        .regex(/^[0-9a-fA-F]{24}$/)
        .messages({
            'string.pattern.base': 'Invalid ID format',
            'any.required': 'ID is required',
        }),
});

// Schema for numeric weight fields (only numbers and decimals)
const numericWeightSchema = Joi.object().keys({
    value: Joi.string().pattern(/^[0-9]*\.?[0-9]*$/).allow(''),
    unit: Joi.string().valid('g', 'mg', 'ct', 'kg').required(),
});

// Schema for alphanumeric weight fields (allows any characters)
const alphanumericWeightSchema = Joi.object().keys({
    value: Joi.string().allow(''),
    unit: Joi.string().valid('g', 'mg', 'ct', 'kg').required(),
});

const priceSchema = Joi.object().keys({
    costPrice: Joi.string().allow('').trim(),
    sellingPrice: Joi.string().allow('').trim(), // Changed to string to allow alphanumeric values like "TBD" or "SP50"
    mrp: Joi.string().allow('').trim(), // Changed to string to allow alphanumeric values
    makingCharges: Joi.number().default(0).min(0),
});

const stockSchema = Joi.object().keys({
    quantity: Joi.number().default(0).min(0),
    minStockLevel: Joi.number().default(1).min(0),
});

// Base inventory validation schema
const baseInventorySchema = {
    category: Joi.string()
        .valid(...Object.values(Categories))
        .required(),
    name: Joi.string().required().trim(),
    description: Joi.string().allow('', null),
    size: Joi.string().allow('').trim(),
    sizes: Joi.array().items(
        Joi.object({
            value: Joi.string().required().trim(),
            quantity: Joi.number().min(0).required()
        })
    ),
    sku: Joi.string().required().trim(),
    purity: Joi.string()
        .valid('ss', '14', '18', '20', '22')
        .when('category', {
            is: Joi.string().valid(Categories.gold, Categories.silver),
            then: Joi.required(),
            otherwise: Joi.optional().allow('', null),
        }),
    weight: Joi.object({
        netWt: numericWeightSchema.optional(),
        grossWt: numericWeightSchema.optional(),
        stoneWt: alphanumericWeightSchema.optional(),
        diamondWt: alphanumericWeightSchema.optional(),
        averageWt: alphanumericWeightSchema.optional(),
    }).required(),
    price: priceSchema.required(),
    stock: stockSchema.required(),
    images: Joi.array().items(Joi.string()),
    qrCode: Joi.string().optional().allow('', null),
    tags: Joi.array().items(Joi.string().trim()),
    status: Joi.string().valid('available', 'sold', 'reserved', 'damaged', 'in-maintenance').default('available'),
    notes: Joi.string().allow('', null),
};

// Single inventory validation
const inventoryValidation = Joi.object(baseInventorySchema);

// Bulk inventory validation
const bulkInventoryValidation = Joi.object({
    items: Joi.array().items(Joi.object(baseInventorySchema)).min(1).required(),
});

// Stock update validation
const stockUpdateValidation = Joi.object({
    quantity: Joi.number().required(),
    operation: Joi.string().valid('add', 'set').default('add'),
});

// Bulk delete validation
const bulkDeleteValidation = Joi.object({
    ids: Joi.array()
        .items(
            Joi.string()
                .regex(/^[0-9a-fA-F]{24}$/)
                .messages({
                    'string.pattern.base': 'Invalid ID format',
                })
        )
        .min(1)
        .required()
        .unique(),
});

// Query parameters validation
const listQueryValidation = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    search: Joi.string().trim(),
    category: Joi.string().valid('gold', 'silver', 'diamond', 'platinum', 'gemstone', 'pearl', 'other'),
    status: Joi.string().valid('available', 'sold', 'reserved', 'damaged', 'in-maintenance'),
    minStock: Joi.number().integer().min(0),
    maxStock: Joi.number().integer().min(Joi.ref('minStock')),
    minPrice: Joi.number().min(0),
    maxPrice: Joi.number().min(Joi.ref('minPrice')),
    sortBy: Joi.string()
        .valid('created', 'updated', 'name', 'sku', 'price.sellingPrice', 'stock.quantity')
        .default('created'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
});

module.exports = {
    requireSlug,
    requireId,
    inventoryValidation,
    bulkInventoryValidation,
    stockUpdateValidation,
    bulkDeleteValidation,
    listQueryValidation,
};
