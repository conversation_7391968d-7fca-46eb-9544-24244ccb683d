import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import EyeIcon from "@mui/icons-material/Visibility";

import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import LocalPrintshopIcon from "@mui/icons-material/LocalPrintshop";
import {
  Box,
  TextField,
  useTheme,
  useMediaQuery,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  Stack,
  InputAdornment,
  Checkbox
} from "@mui/material";
import { LuxuryButton, PrintLabel } from "components";
import { Search, Clear } from "@mui/icons-material";
import { useState, useMemo } from "react";
import { Link, useSearchParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { useDebounce, useDeleteRecord } from "hooks";
import { useDeleteInventoryMutation, useGetInventoriesQuery } from "services";
import Header from "components/Header";
import StyledDataGrid from "components/datagrids/StyledDataGrid";
import { tokens } from "theme";
import dataGridStyles from "../../styles/dataGridStyles";
import { setConfirmModalConfig } from "store/slices/utilSlice";
import { formatMediaUrl } from "utils/common";
import { generateBulkLabelsHtml } from "components/LabelDesign";

const List = () => {
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useSearchParams();
  const searchName = searchParams.get("name") || "";
  const debouncedSearch = useDebounce(searchName, 500);
  const [selectedItems, setSelectedItems] = useState([]);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userInfo = useSelector((state) => state.auth.user);
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const styles = dataGridStyles(theme.palette.mode);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const {
    data = {},
    isLoading,
    refetch,
  } = useGetInventoriesQuery(
    {
      params: {
        page: page + 1,
        limit: pageSize,
        search: debouncedSearch ?? undefined,
      },
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const { items: inventoryData = [], pagination = {} } = data ?? {};
  const [deleteInventory] = useDeleteInventoryMutation();
  const { deleteRecord } = useDeleteRecord(deleteInventory);

  // Clone inventory function
  const handleCloneInventory = (item) => {
    // Create a clone object with all data except _id, sku, and timestamps
    const cloneData = {
      ...item,
      _id: undefined,
      sku: '', // Will be manually entered
      created: undefined,
      updated: undefined,
      __v: undefined,
      // Reset some fields for new entry
      stock: {
        ...item.stock,
        quantity: 0, // Reset quantity for new item
      },
      sizes: item.sizes?.map(size => ({
        ...size,
        _id: undefined,
        id: undefined,
        quantity: 0, // Reset size quantities
      })) || [],
    };

    // Navigate to add page with clone data
    navigate('/inventory/add', {
      state: {
        cloneData,
        isClone: true
      }
    });
  };

  // Handle selection
  const handleSelectItem = (itemId) => {
    setSelectedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedItems.length === inventoryData.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(inventoryData.map(item => item._id));
    }
  };

  // Bulk print function using common LabelDesign component
  const handleBulkPrint = () => {
    if (selectedItems.length === 0) {
      alert("Please select items to print");
      return;
    }

    const selectedInventoryItems = inventoryData.filter(item =>
      selectedItems.includes(item._id)
    );

    // Use the common label design component
    const html = generateBulkLabelsHtml(selectedInventoryItems);

    // Print the labels using the same method as PrintLabel component
    const win = window.open("", "_blank", "height=600,width=800");
    if (!win) {
      alert("Please allow pop-ups to print labels. Check your browser's popup blocker settings.");
      return;
    }

    setTimeout(() => {
      try {
        if (!win || !win.document) {
          alert("Unable to open print window. Please check your browser settings.");
          return;
        }

        win.document.open();
        win.document.write(html);
        win.document.close();

        setTimeout(() => {
          try {
            win.focus();
            win.print();
            win.close();
          } catch (printError) {
            console.error("Print error:", printError);
            alert("There was an issue printing the labels. Please try again.");
          }
        }, 500);
      } catch (error) {
        console.error("Window creation error:", error);
        alert("Unable to create print window. Please check your browser settings.");
        if (win) {
          win.close();
        }
      }
    }, 100);
  };



  // Define columns with responsive considerations
  const columns = useMemo(() => [
    {
      field: "select",
      headerName: "",
      width: 50,
      maxWidth: 50,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderHeader: () => (
        <Checkbox
          checked={selectedItems.length === inventoryData.length && inventoryData.length > 0}
          indeterminate={selectedItems.length > 0 && selectedItems.length < inventoryData.length}
          onChange={handleSelectAll}
          size="small"
        />
      ),
      renderCell: (params) => (
        <Checkbox
          checked={selectedItems.includes(params.row._id)}
          onChange={() => handleSelectItem(params.row._id)}
          size="small"
        />
      ),
    },
    {
      field: "srNo",
      headerName: "Sr. No",
      width: 80,
      maxWidth: 80,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderCell: (params) => {
        const index = inventoryData.findIndex(item => item._id === params.row._id);
        return (
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {page * pageSize + index + 1}
          </Typography>
        );
      },
    },
    {
      field: "sku",
      headerName: "SKU ID",
      width: 140,
      minWidth: 120,
      filterable: true,
    },
    {
      field: "name",
      headerName: "Name",
      width: 200,
      minWidth: 150,
      filterable: true,
      renderCell: (params) => (
        <Tooltip title={params.value} enterDelay={500}>
          <span style={{
            textTransform: "capitalize",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis"
          }}>
            {params.value}
          </span>
        </Tooltip>
      ),
    },
    {
      field: "category",
      headerName: "Category",
      width: 120,
      minWidth: 100,
      filterable: true,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          sx={{
            textTransform: "capitalize",
            backgroundColor: colors.primary[100],
            color: colors.primary[700],
          }}
        />
      ),
    },
    {
      field: "price.sellingPrice",
      headerName: "Price",
      width: 110,
      minWidth: 90,
      type: 'number',
      filterable: true,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            fontWeight: 600,
            color: colors.primary[500]
          }}
        >
          ${params.row.price?.sellingPrice?.toLocaleString() || 0}
        </Typography>
      ),
    },
    {
      field: "stock.quantity",
      headerName: "Stock",
      width: 90,
      minWidth: 70,
      type: 'number',
      filterable: true,
      renderCell: (params) => {
        const stockQty = params.row.stock?.quantity ?? 0;
        const isLowStock = stockQty <= 5;
        return (
          <Chip
            label={stockQty}
            size="small"
            sx={{
              backgroundColor: isLowStock ? colors.redAccent[100] : colors.greenAccent[100],
              color: isLowStock ? colors.redAccent[700] : colors.greenAccent[700],
              fontWeight: 600
            }}
          />
        );
      },
    },
    {
      field: "images",
      headerName: "Image",
      width: 90,
      minWidth: 80,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderCell: (params) => {
        const imageUrl = params.row.images?.[0];
        return imageUrl ? (
          <Box
            sx={{
              width: 50,
              height: 50,
              borderRadius: '8px',
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: `1px solid ${colors.whiteAccent[300]}`,
            }}
          >
            <img
              src={formatMediaUrl(imageUrl)}
              alt={params.row.name}
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          </Box>
        ) : (
          <Box
            sx={{
              width: 50,
              height: 50,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: colors.whiteAccent[200],
              color: colors.blackAccent[500],
              fontSize: '0.75rem',
            }}
          >
            No Image
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 200,
      minWidth: 180,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderCell: (params) => (
        <Stack direction="row" spacing={1} justifyContent="center">
          {userInfo?.role === "ADMIN" && (
            <>
              <Tooltip title="Edit">
                <IconButton
                  component={Link}
                  to={`/inventory/edit/${params.row._id}`}
                  size="small"
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.primary[500] }
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="Clone">
                <IconButton
                  size="small"
                  onClick={() => handleCloneInventory(params.row)}
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.blueAccent[500] }
                  }}
                >
                  <ContentCopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  size="small"
                  onClick={() => {
                    const payload = {
                      id: params.row._id,
                    };
                    dispatch(
                      setConfirmModalConfig({
                        visible: true,
                        data: {
                          onSubmit: () => deleteRecord(payload, refetch),
                          content: {
                            heading: "Delete Inventory?",
                            description:
                              "Are you sure you want to delete this inventory item?",
                          },
                        },
                      })
                    );
                  }}
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.redAccent[500] }
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </>
          )}

          <Tooltip title="View Details">
            <IconButton
              component={Link}
              to={`/inventory/${params.row._id}`}
              size="small"
              sx={{
                color: colors.blackAccent[700],
                '&:hover': { color: colors.primary[500] }
              }}
            >
              <EyeIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Box sx={{ display: 'inline-block' }}>
            <PrintLabel
              item={params.row}
              buttonText=""
              buttonVariant="text"
              buttonSize="small"
              buttonSx={{
                minWidth: 'auto',
                p: 1,
                color: colors.blackAccent[700],
                '&:hover': { color: colors.primary[500], backgroundColor: 'transparent' }
              }}
              showIcon={true}
            />
          </Box>
        </Stack>
      ),
    },
  ], [colors, dispatch, userInfo, selectedItems, inventoryData, page, pageSize, handleCloneInventory, handleSelectItem]);

  const handleSearchChange = (evt) => {
    setSearchParams({
      name: evt.target.value,
    });
  };

  const handleClearSearch = () => {
    setSearchParams({});
  };



  return (
    <Box m={{ xs: "10px", sm: "15px", md: "20px" }} maxWidth="100%" overflow="hidden">
      <Box
        sx={{
          ...styles.headingsContainer,
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          alignItems: { xs: "flex-start", sm: "center" },
          justifyContent: "space-between",
          gap: { xs: 2, sm: 0 },
          mb: { xs: 2, sm: 3 }
        }}
      >
        <Box sx={styles.mainHeadings}>
          <Header title="INVENTORY" subtitle="Managing Inventory Items" />
        </Box>
        <Box sx={{
          alignSelf: { xs: "flex-start", sm: "auto" },
          display: "flex",
          gap: 2,
          flexDirection: { xs: 'column', sm: 'row' }
        }}>
          {/* Bulk Print Button */}
          {selectedItems.length > 0 && (
            <LuxuryButton
              onClick={handleBulkPrint}
              variant="outlined"
              startIcon={<LocalPrintshopIcon />}
              sx={{
                width: { xs: '100%', sm: 'auto' },
                borderColor: colors.primary[500],
                color: colors.primary[500],
                '&:hover': {
                  borderColor: colors.primary[600],
                  backgroundColor: colors.primary[50],
                }
              }}
            >
              Print Selected ({selectedItems.length})
            </LuxuryButton>
          )}

          {userInfo?.role === "ADMIN" && (
            <LuxuryButton
              component={Link}
              to="/inventory/add"
              startIcon={<AddOutlinedIcon />}
              sx={{ width: { xs: '100%', sm: 'auto' } }}
            >
              Add Inventory
            </LuxuryButton>
          )}
        </Box>
      </Box>

      <Box
        sx={{
          ...styles.formContainer,
          p: { xs: 1, sm: 2 },
          borderRadius: '12px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'stretch', sm: 'center' },
            justifyContent: 'space-between',
            gap: 2,
            mb: 2,
          }}
        >
          <TextField
            placeholder="Search by name, SKU, or description"
            onChange={handleSearchChange}
            value={searchName}
            fullWidth
            size={isMobile ? "small" : "medium"}
            sx={{
              maxWidth: { sm: '400px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: searchName && (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear search"
                    onClick={handleClearSearch}
                    edge="end"
                    size="small"
                  >
                    <Clear fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {selectedItems.length > 0 && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" color="primary.main" sx={{ fontWeight: 500 }}>
                  {selectedItems.length} selected
                </Typography>
                <LuxuryButton
                  size="small"
                  variant="text"
                  onClick={() => setSelectedItems([])}
                  sx={{ minWidth: 'auto', p: 0.5 }}
                >
                  Clear
                </LuxuryButton>
              </Box>
            )}
            <Box sx={{ display: { xs: 'none', md: 'block' } }}>
              <Typography variant="body2" color="text.secondary">
                {pagination.totalItems || 0} items found
              </Typography>
            </Box>
          </Box>
        </Box>

        <div id="inventory-grid">
          <StyledDataGrid
            rows={inventoryData}
            columns={columns}
            getRowId={(row) => row._id}
            loading={isLoading}
            pagination
            page={page}
            pageSize={pageSize}
            rowCount={pagination.totalItems || 0}
            paginationMode="server"
            onPageChange={(newPage) => setPage(newPage)}
            onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
            className="no-shadow"
            noResultText="No inventory items found."
            enableFiltering={true}
            enableColumnVisibilityToggle={true}
            enableDensitySelector={true}
            enableExport={true}
            containerStyle={{
              borderRadius: '8px',
              height: isMobile ? '60vh' : isTablet ? '65vh' : '70vh',
            }}
            customStyle={{
              '& .MuiDataGrid-toolbarContainer': {
                padding: isMobile ? '8px 4px' : '16px 8px',
                flexWrap: 'wrap',
                gap: '8px',
              },
            }}
            initialState={{
              sorting: {
                sortModel: [{ field: 'name', sort: 'asc' }],
              },
            }}
          />
        </div>
      </Box>


    </Box>
  );
};

export default List;
