import React from 'react';
import { Button } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * LuxuryButton component - A styled button component for the jewelry business management system
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='contained'] - Button variant ('contained', 'outlined')
 * @param {string} [props.color='primary'] - Button color ('primary', 'secondary', 'success', 'danger')
 * @param {string} [props.size='medium'] - Button size ('small', 'medium', 'large')
 * @param {function} [props.onClick] - Click handler function
 * @param {boolean} [props.disabled=false] - Whether the button is disabled
 * @param {string} [props.type='button'] - Button type ('button', 'submit', 'reset')
 * @param {boolean} [props.fullWidth=false] - Whether the button should take full width
 * @param {React.ReactNode} [props.startIcon] - Icon to display at the start of the button
 * @param {React.ReactNode} [props.endIcon] - Icon to display at the end of the button
 * @param {React.ReactNode} props.children - Button content
 * @param {Object} [props.sx] - Additional styles to apply to the button
 */
const LuxuryButton = ({
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  onClick,
  disabled = false,
  type = 'button',
  fullWidth = false,
  startIcon,
  endIcon,
  children,
  sx = {},
  ...rest
}) => {
  // Map our custom props to Material-UI props and CSS classes
  const getButtonClasses = () => {
    const classes = ['luxury-button'];
    
    // Add variant class
    if (variant === 'outlined') {
      classes.push('outlined');
    }
    
    // Add color class
    if (color !== 'primary') {
      classes.push(color);
    }
    
    // Add size class
    if (size === 'small') {
      classes.push('small');
    } else if (size === 'large') {
      classes.push('large');
    }
    
    return classes.join(' ');
  };
  
  // Map our custom size to Material-UI size
  const getMuiSize = () => {
    if (size === 'small') return 'small';
    if (size === 'large') return 'large';
    return 'medium';
  };
  
  // Map our custom variant to Material-UI variant
  const getMuiVariant = () => {
    if (variant === 'outlined') return 'outlined';
    return 'contained';
  };
  
  return (
    <Button
      className={getButtonClasses()}
      variant={getMuiVariant()}
      size={getMuiSize()}
      onClick={onClick}
      disabled={disabled}
      type={type}
      fullWidth={fullWidth}
      startIcon={startIcon}
      endIcon={endIcon}
      sx={sx}
      {...rest}
    >
      {children}
    </Button>
  );
};

LuxuryButton.propTypes = {
  variant: PropTypes.oneOf(['contained', 'outlined']),
  color: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  fullWidth: PropTypes.bool,
  startIcon: PropTypes.node,
  endIcon: PropTypes.node,
  children: PropTypes.node.isRequired,
  sx: PropTypes.object,
};

export default LuxuryButton;
