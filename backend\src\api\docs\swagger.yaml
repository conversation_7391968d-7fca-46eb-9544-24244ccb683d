openapi: 3.0.0
info:
  title: StarColors Business Management System API
  version: 1.0.0
  description: API documentation for StarColors BMS

servers:
  - url: /api
    description: API server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: Error message
        data:
          type: object
          nullable: true

    Success:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Success message
        data:
          type: object
          nullable: true

security:
  - bearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check endpoint
      description: Returns the health status of the API
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Success'
        '500':
          description: API is not healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error' 