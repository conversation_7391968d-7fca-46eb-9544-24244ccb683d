const { Joi, patterns } = require('../../util/validations');

const uploadFile = Joi.object().keys({
    location: Joi.string()
        .trim()
        .required(),
    type: Joi.string()
        .valid('IMAGE', 'DOCUMENT.PDF')
        .required(),
    count: Joi.string()
        .regex(patterns.number, 'numberPattern')
        .required(),
});

const uploadMedia = Joi.object().keys({
    type: Joi.string()
        .valid('image', 'video')
        .required(),
    folder: Joi.string()
        .trim()
        .required(),
}).unknown(true); // This allows additional fields like 'media'

module.exports = {
    uploadFile,
    uploadMedia
};
