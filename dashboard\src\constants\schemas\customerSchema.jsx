import * as Yup from "yup";

export const customerInitialValues = {
  name: "",
  email: "",
  phone: "",
  alternatePhone: "",
  address: {
    street: "",
    city: "",
    state: "",
    postalCode: "",
    country: "India",
  },
  gstNumber: "",
  panCardNumber: "",
  aadharNumber: "",
  dateOfBirth: "",
  anniversary: "",
  customerType: "retail",
  tags: [],
  isActive: true,
};

export const customerValidationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  email: Yup.string().email("Invalid email").required("Email is required"),
  phone: Yup.string().required("Phone is required"),
  alternatePhone: Yup.string(),
  address: Yup.object().shape({
    street: Yup.string().required("Street is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    postalCode: Yup.string().required("Postal Code is required"),
    country: Yup.string().required("Country is required"),
  }),
  gstNumber: Yup.string(),
  panCardNumber: Yup.string(),
  aadharNumber: Yup.string(),
  dateOfBirth: Yup.date().nullable(),
  anniversary: Yup.date().nullable(),
  customerType: Yup.string().oneOf(["retail", "other"], "Invalid customer type").required("Customer type is required"),
  tags: Yup.array().of(Yup.string()),
  isActive: Yup.boolean(),
});
