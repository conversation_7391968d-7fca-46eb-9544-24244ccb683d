import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  IconButton,
  Grid,
  Divider,
  Paper,
  useTheme,
  Tooltip,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { LuxuryButton } from 'components';
import Header from 'components/Header';
import { tokens } from '../../theme';
import { generateLabelHtml } from 'components/LabelDesign';

const DynamicLabel = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  // State for label fields
  const [labelFields, setLabelFields] = useState([
    { id: 1, name: '', value: '' }
  ]);

  // State for label settings
  const [labelSettings, setLabelSettings] = useState({
    title: '',
    width: '60mm',
    height: '15mm',
    fontSize: '10px',
    includeQR: false,
    qrText: '',
  });

  // State for preview
  const [showPreview, setShowPreview] = useState(false);

  // Custom styles
  const sectionStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.08)",
      overflow: "hidden",
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      height: "100%",
      "&:hover": {
        transform: "translateY(-2px)",
        boxShadow: "0px 8px 30px rgba(0, 0, 0, 0.12)",
      },
    },
    sectionTitle: {
      fontFamily: '"Playfair Display", serif',
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: "relative",
      display: "inline-block",
      paddingBottom: "8px",
      "&::after": {
        content: '""',
        position: "absolute",
        width: "40%",
        height: "2px",
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
  };

  // Add new field
  const addField = () => {
    const newId = Math.max(...labelFields.map(f => f.id)) + 1;
    setLabelFields([...labelFields, { id: newId, name: '', value: '' }]);
  };

  // Remove field
  const removeField = (id) => {
    if (labelFields.length > 1) {
      setLabelFields(labelFields.filter(field => field.id !== id));
    }
  };

  // Update field
  const updateField = (id, key, value) => {
    setLabelFields(labelFields.map(field =>
      field.id === id ? { ...field, [key]: value } : field
    ));
  };

  // Clear all fields
  const clearAllFields = () => {
    setLabelFields([{ id: 1, name: '', value: '' }]);
    setLabelSettings({
      title: '',
      width: '60mm',
      height: '15mm',
      fontSize: '10px',
      includeQR: false,
      qrText: '',
    });
    setShowPreview(false);
  };

  // Generate label HTML using common component
  const generateDynamicLabelHtml = () => {
    const validFields = labelFields.filter(field => field.name && field.value);

    // Create a mock inventory item from dynamic fields
    const mockItem = {
      name: labelSettings.title || 'Dynamic Label',
      sku: '',
      size: '',
      weight: {},
      price: {},
      qrCode: labelSettings.includeQR && labelSettings.qrText ? 'mock-qr' : null
    };

    // Map dynamic fields to inventory structure
    validFields.forEach(field => {
      const fieldName = field.name.toLowerCase();

      if (fieldName.includes('sku')) {
        mockItem.sku = field.value;
      } else if (fieldName.includes('size')) {
        mockItem.size = field.value;
      } else if (fieldName.includes('gw') || fieldName.includes('gross')) {
        mockItem.weight.grossWt = { value: field.value, unit: 'ct' };
      } else if (fieldName.includes('nw') || fieldName.includes('net')) {
        mockItem.weight.netWt = { value: field.value, unit: 'ct' };
      } else if (fieldName.includes('sw') || fieldName.includes('stone')) {
        mockItem.weight.stoneWt = { value: field.value, unit: 'ct' };
      } else if (fieldName.includes('dw') || fieldName.includes('diamond')) {
        mockItem.weight.diamondWt = { value: field.value, unit: 'ct' };
      } else if (fieldName.includes('price') || fieldName.includes('sp') || fieldName.includes('selling')) {
        const numericValue = parseFloat(field.value.replace(/[^0-9.]/g, ''));
        if (!isNaN(numericValue)) {
          mockItem.price.sellingPrice = numericValue;
        }
      } else if (fieldName.includes('cost') || fieldName.includes('cp')) {
        const numericValue = parseFloat(field.value.replace(/[^0-9.]/g, ''));
        if (!isNaN(numericValue)) {
          mockItem.price.costPrice = numericValue;
        }
      }
    });

    // Use the common label design with custom styling for dynamic labels
    let html = generateLabelHtml(mockItem, null);

    // Customize the HTML for dynamic label dimensions and styling
    html = html.replace(/width: 60mm;/g, `width: ${labelSettings.width};`);
    html = html.replace(/height: 15mm;/g, `height: ${labelSettings.height};`);
    html = html.replace(/font-size:10.5px;/g, `font-size: ${labelSettings.fontSize};`);
    html = html.replace(/font-size: 10.5px;/g, `font-size: ${labelSettings.fontSize};`);

    // Add any unmapped fields as additional content
    const unmappedFields = validFields.filter(field => {
      const fieldName = field.name.toLowerCase();
      return !fieldName.includes('sku') &&
             !fieldName.includes('size') &&
             !fieldName.includes('gw') && !fieldName.includes('gross') &&
             !fieldName.includes('nw') && !fieldName.includes('net') &&
             !fieldName.includes('sw') && !fieldName.includes('stone') &&
             !fieldName.includes('dw') && !fieldName.includes('diamond') &&
             !fieldName.includes('price') && !fieldName.includes('sp') && !fieldName.includes('selling') &&
             !fieldName.includes('cost') && !fieldName.includes('cp');
    });

    if (unmappedFields.length > 0) {
      const additionalContent = unmappedFields.map(field =>
        `<div style="font-size: ${labelSettings.fontSize}; margin-bottom: 1px;">
          <strong>${field.name}:</strong> ${field.value}
        </div>`
      ).join('');

      // Insert additional content before the closing table tag
      html = html.replace('</table>', `</table><div style="margin-top: 2px;">${additionalContent}</div>`);
    }

    return html;
  };

  // Print label with preview dialog (same as PrintLabel component)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [labelHtml, setLabelHtml] = useState('');

  const handlePreviewLabel = () => {
    const html = generateDynamicLabelHtml();
    setLabelHtml(html);
    setPreviewDialogOpen(true);
  };

  const printLabel = () => {
    const html = generateDynamicLabelHtml();

    // Try popup method first (same as PrintLabel component)
    const win = window.open("", "_blank", "height=400,width=800");
    if (!win) {
      alert("Please allow pop-ups to print labels. Check your browser's popup blocker settings.");
      return;
    }

    setTimeout(() => {
      try {
        if (!win || !win.document) {
          alert("Unable to open print window. Please check your browser settings.");
          return;
        }

        win.document.open();
        win.document.write(html);
        win.document.close();

        setTimeout(() => {
          try {
            win.focus();
            win.print();
            win.close();
          } catch (printError) {
            console.error("Print error:", printError);
            alert("There was an issue printing the label. Please try again.");
          }
        }, 500);
      } catch (error) {
        console.error("Window creation error:", error);
        alert("Unable to create print window. Please check your browser settings.");
        if (win) {
          win.close();
        }
      }
    }, 100);
  };

  // Alternative print method using iframe (fallback for popup blockers)
  const printWithIframe = () => {
    if (!labelHtml) return;

    // Create a hidden iframe
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.top = '-1000px';
    iframe.style.left = '-1000px';
    iframe.style.width = '1px';
    iframe.style.height = '1px';
    iframe.style.border = 'none';

    document.body.appendChild(iframe);

    // Write content to iframe
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(labelHtml);
    iframeDoc.close();

    // Wait for content to load, then print
    setTimeout(() => {
      try {
        iframe.contentWindow.focus();
        iframe.contentWindow.print();
      } catch (error) {
        console.error("Iframe print error:", error);
        alert("There was an issue printing the label. Please try again.");
      } finally {
        // Clean up
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);
      }
    }, 500);
  };

  return (
    <Box m="20px">
      <Header
        title="DYNAMIC LABEL CREATOR"
        subtitle="Create custom labels with dynamic fields and print them"
      />

      <Grid container spacing={3}>
        {/* Left Column - Label Configuration */}
        <Grid item xs={12} md={8}>
          <Card sx={sectionStyles.card}>
            <CardContent>
              <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                Label Configuration
              </Typography>
              <Divider sx={sectionStyles.divider} />

              {/* Label Settings */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Label Title (Optional)"
                    value={labelSettings.title}
                    onChange={(e) => setLabelSettings({...labelSettings, title: e.target.value})}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Width</InputLabel>
                    <Select
                      value={labelSettings.width}
                      label="Width"
                      onChange={(e) => setLabelSettings({...labelSettings, width: e.target.value})}
                    >
                      <MenuItem value="40mm">40mm</MenuItem>
                      <MenuItem value="50mm">50mm</MenuItem>
                      <MenuItem value="60mm">60mm</MenuItem>
                      <MenuItem value="70mm">70mm</MenuItem>
                      <MenuItem value="80mm">80mm</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Height</InputLabel>
                    <Select
                      value={labelSettings.height}
                      label="Height"
                      onChange={(e) => setLabelSettings({...labelSettings, height: e.target.value})}
                    >
                      <MenuItem value="10mm">10mm</MenuItem>
                      <MenuItem value="13mm">13mm</MenuItem>
                      <MenuItem value="15mm">15mm</MenuItem>
                      <MenuItem value="20mm">20mm</MenuItem>
                      <MenuItem value="25mm">25mm</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              {/* Dynamic Fields */}
              <Typography variant="h6" sx={{ mb: 2, color: colors.blackAccent[700] }}>
                Label Fields
              </Typography>

              {labelFields.map((field, index) => (
                <Grid container spacing={2} key={field.id} sx={{ mb: 2 }}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Field Name"
                      value={field.name}
                      onChange={(e) => updateField(field.id, 'name', e.target.value)}
                      placeholder="e.g., SKU, Weight, Price"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Field Value"
                      value={field.value}
                      onChange={(e) => updateField(field.id, 'value', e.target.value)}
                      placeholder="Enter the value"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Box sx={{ display: 'flex', gap: 1, height: '100%', alignItems: 'center' }}>
                      {index === labelFields.length - 1 && (
                        <Tooltip title="Add Field">
                          <IconButton
                            onClick={addField}
                            sx={{
                              backgroundColor: colors.primary[100],
                              color: colors.primary[700],
                              '&:hover': { backgroundColor: colors.primary[200] }
                            }}
                          >
                            <AddIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                      {labelFields.length > 1 && (
                        <Tooltip title="Remove Field">
                          <IconButton
                            onClick={() => removeField(field.id)}
                            sx={{
                              backgroundColor: colors.redAccent[100],
                              color: colors.redAccent[700],
                              '&:hover': { backgroundColor: colors.redAccent[200] }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              ))}

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, mt: 3, flexWrap: 'wrap' }}>
                <LuxuryButton
                  onClick={() => setShowPreview(!showPreview)}
                  variant="outlined"
                  startIcon={<PreviewIcon />}
                >
                  {showPreview ? 'Hide Preview' : 'Show Preview'}
                </LuxuryButton>

                <LuxuryButton
                  onClick={handlePreviewLabel}
                  variant="contained"
                  startIcon={<PrintIcon />}
                  disabled={!labelFields.some(field => field.name && field.value)}
                >
                  Print Label
                </LuxuryButton>

                <LuxuryButton
                  onClick={clearAllFields}
                  variant="text"
                  startIcon={<ClearIcon />}
                  sx={{ color: colors.redAccent[500] }}
                >
                  Clear All
                </LuxuryButton>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Right Column - Preview */}
        <Grid item xs={12} md={4}>
          <Card sx={sectionStyles.card}>
            <CardContent>
              <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                Label Preview
              </Typography>
              <Divider sx={sectionStyles.divider} />

              {showPreview ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Paper
                    elevation={3}
                    sx={{
                      width: labelSettings.width,
                      height: labelSettings.height,
                      border: `1px solid ${colors.blackAccent[300]}`,
                      fontFamily: 'Arial, sans-serif',
                      fontSize: labelSettings.fontSize,
                      lineHeight: 1.2,
                      overflow: 'hidden',
                      backgroundColor: colors.whiteAccent[100],
                      position: 'relative',
                      p: '1mm',
                    }}
                  >
                    {/* Title Section */}
                    {labelSettings.title && (
                      <Box sx={{
                        fontWeight: 'bold',
                        textAlign: 'center',
                        borderBottom: `1px solid ${colors.blackAccent[300]}`,
                        mb: '1mm',
                        pb: '1mm',
                        fontSize: labelSettings.fontSize,
                      }}>
                        {labelSettings.title}
                      </Box>
                    )}

                    {/* Smart Layout Container */}
                    <Box sx={{
                      width: '100%',
                      height: labelSettings.title ? 'calc(100% - 15px)' : '100%',
                      position: 'relative'
                    }}>
                      {(() => {
                        const validFields = labelFields.filter(field => field.name && field.value);
                        const totalFields = validFields.length;
                        const hasQR = labelSettings.includeQR && labelSettings.qrText;

                        // Calculate available space and determine layout
                        const labelWidthNum = parseInt(labelSettings.width);
                        const labelHeightNum = parseInt(labelSettings.height);

                        // Estimate how many fields can fit in single column based on label height
                        const estimatedFieldHeight = 3; // mm per field
                        const availableHeight = labelHeightNum - (labelSettings.title ? 8 : 4); // Reserve space for title and padding
                        const maxFieldsInSingleColumn = Math.floor(availableHeight / estimatedFieldHeight);

                        if (totalFields <= maxFieldsInSingleColumn && !hasQR) {
                          // Single column layout
                          return (
                            <Box sx={{
                              width: '100%',
                              height: '100%',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'flex-start'
                            }}>
                              {validFields.map((field, index) => (
                                <Box
                                  key={index}
                                  sx={{
                                    mb: '0.5px',
                                    fontSize: labelSettings.fontSize,
                                    lineHeight: 1.0,
                                    wordBreak: 'break-word',
                                    overflow: 'hidden',
                                    whiteSpace: 'nowrap',
                                    textOverflow: 'ellipsis',
                                  }}
                                >
                                  <strong>{field.name}:</strong> {field.value}
                                </Box>
                              ))}
                            </Box>
                          );
                        } else {
                          // Multi-column layout
                          const leftColumnFields = Math.ceil(totalFields / 2);
                          const leftFields = validFields.slice(0, leftColumnFields);
                          const rightFields = validFields.slice(leftColumnFields);

                          return (
                            <Box sx={{
                              display: 'flex',
                              width: '100%',
                              height: '100%',
                              gap: '1mm'
                            }}>
                              {/* Left Column */}
                              <Box sx={{
                                width: hasQR ? '38%' : '48%',
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'flex-start'
                              }}>
                                {leftFields.map((field, index) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      mb: '0.5px',
                                      fontSize: labelSettings.fontSize,
                                      lineHeight: 1.0,
                                      wordBreak: 'break-word',
                                      overflow: 'hidden',
                                      whiteSpace: 'nowrap',
                                      textOverflow: 'ellipsis',
                                    }}
                                  >
                                    <strong>{field.name}:</strong> {field.value}
                                  </Box>
                                ))}
                              </Box>

                              {/* Right Column */}
                              <Box sx={{
                                width: hasQR ? '38%' : '48%',
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'flex-start'
                              }}>
                                {rightFields.map((field, index) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      mb: '0.5px',
                                      fontSize: labelSettings.fontSize,
                                      lineHeight: 1.0,
                                      wordBreak: 'break-word',
                                      overflow: 'hidden',
                                      whiteSpace: 'nowrap',
                                      textOverflow: 'ellipsis',
                                    }}
                                  >
                                    <strong>{field.name}:</strong> {field.value}
                                  </Box>
                                ))}
                              </Box>

                              {/* QR Section */}
                              {hasQR && (
                                <Box sx={{
                                  width: '22%',
                                  height: '100%',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}>
                                  <Box sx={{
                                    width: '6mm',
                                    height: '6mm',
                                    border: `1px solid ${colors.blackAccent[300]}`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '5px',
                                    textAlign: 'center',
                                    mb: '0.5mm'
                                  }}>
                                    QR
                                  </Box>
                                  <Box sx={{
                                    fontSize: '5px',
                                    textAlign: 'center',
                                    wordBreak: 'break-all',
                                    lineHeight: 0.9
                                  }}>
                                    {labelSettings.qrText}
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          );
                        }
                      })()}
                    </Box>
                  </Paper>
                </Box>
              ) : (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '200px',
                  color: colors.blackAccent[500]
                }}>
                  <PreviewIcon sx={{ fontSize: 60, mb: 2 }} />
                  <Typography variant="body1" textAlign="center">
                    Click "Show Preview" to see how your label will look
                  </Typography>
                </Box>
              )}

              <Box sx={{ mt: 3 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Instructions:</strong>
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                  • Add field names and values above<br/>
                  • Use the preview to check your label<br/>
                  • Click "Print Label" to print<br/>
                  • Adjust size settings as needed<br/>
                  • For many fields, use larger label sizes
                </Typography>

                {/* Warning for too many fields */}
                {labelFields.filter(field => field.name && field.value).length > 8 && (
                  <Box sx={{
                    mt: 2,
                    p: 1,
                    backgroundColor: colors.redAccent[50],
                    border: `1px solid ${colors.redAccent[200]}`,
                    borderRadius: '4px'
                  }}>
                    <Typography variant="body2" color={colors.redAccent[700]} sx={{ fontSize: '0.8rem' }}>
                      ⚠️ <strong>Warning:</strong> You have {labelFields.filter(field => field.name && field.value).length} fields.
                      Consider using a larger label size or reducing the number of fields for better readability.
                    </Typography>
                  </Box>
                )}

                {/* Field count info */}
                {labelFields.filter(field => field.name && field.value).length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                      Current fields: {labelFields.filter(field => field.name && field.value).length}
                    </Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Print Preview Dialog (same as PrintLabel component) */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogTitle sx={{
          background: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[600]} 100%)`,
          color: colors.whiteAccent[100],
          fontFamily: '"Playfair Display", serif',
          fontWeight: 600,
          textAlign: 'center',
          py: 2
        }}>
          <PrintIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Label Print Preview
        </DialogTitle>

        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Typography variant="body2" sx={{ mb: 3, color: colors.blackAccent[700] }}>
            Preview your label before printing. Click "Print" to send to printer.
          </Typography>

          <Box sx={{
            border: `1px solid ${colors.primary[200]}`,
            borderRadius: '8px',
            p: 2,
            mb: 2,
            backgroundColor: colors.whiteAccent[100],
            overflow: 'auto'
          }}>
            <iframe
              srcDoc={labelHtml}
              title="Label Preview"
              width="100%"
              height="200px"
              style={{ border: 'none' }}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setPreviewDialogOpen(false)}
            sx={{
              color: colors.blackAccent[700],
              '&:hover': {
                backgroundColor: colors.blackAccent[100],
              }
            }}
          >
            Cancel
          </Button>
          <LuxuryButton
            onClick={() => {
              setPreviewDialogOpen(false);

              // Same print logic as PrintLabel component
              const win = window.open("", "_blank", "height=400,width=800");
              if (!win) {
                alert("Please allow pop-ups to print labels. Check your browser's popup blocker settings.");
                return;
              }

              setTimeout(() => {
                try {
                  if (!win || !win.document) {
                    alert("Unable to open print window. Please check your browser settings.");
                    return;
                  }

                  // Write the HTML content to the new window
                  win.document.open();
                  win.document.write(labelHtml);
                  win.document.close();

                  // Wait a bit more for content to load, then print
                  setTimeout(() => {
                    try {
                      win.focus();
                      win.print();
                      win.close();
                    } catch (printError) {
                      console.error("Print error:", printError);
                      alert("There was an issue printing the label. Please try again.");
                    }
                  }, 500);
                } catch (error) {
                  console.error("Window creation error:", error);
                  alert("Unable to create print window. Please check your browser settings.");
                  if (win) {
                    win.close();
                  }
                }
              }, 100);
            }}
            variant="contained"
            startIcon={<PrintIcon />}
          >
            Print
          </LuxuryButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DynamicLabel;
