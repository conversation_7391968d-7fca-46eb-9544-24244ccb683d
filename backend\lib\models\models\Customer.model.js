const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
    },
    phone: {
      type: String,
      required: true,
      trim: true,
    },
    alternatePhone: {
      type: String,
      trim: true,
    },
    address: {
      street: {
        type: String,
        trim: true,
      },
      city: {
        type: String,
        trim: true,
      },
      state: {
        type: String,
        trim: true,
      },
      postalCode: {
        type: String,
        trim: true,
      },
      country: {
        type: String,
        default: 'India',
        trim: true,
      },
    },
    gstNumber: {
      type: String,
      trim: true,
    },
    panCardNumber: {
      type: String,
      trim: true,
    },
    aadharNumber: {
      type: String,
      trim: true,
    },
    dateOfBirth: {
      type: Date,
    },
    anniversary: {
      type: Date,
    },
    profilePicture: {
      type: String,
    },
    creditBalance: {
      type: Number,
      default: 0,
    },
    loyaltyPoints: {
      type: Number,
      default: 0,
    },
    customerType: {
      type: String,
      enum: ['retail', 'wholesale', 'corporate', 'vip'],
      default: 'retail',
    },
    measurements: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Measurement',
      },
    ],
    tags: [
      {
        type: String,
        trim: true,
      },
    ],
    notes: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual fields for invoices
customerSchema.virtual('invoices', {
  ref: 'Invoice',
  localField: '_id',
  foreignField: 'customer',
});

// Compound index for faster searching
customerSchema.index({ name: 1, phone: 1, email: 1 });

// Check if the model has already been compiled
const Customer = mongoose.models.Customer || mongoose.model('Customer', customerSchema);

module.exports = Customer;
