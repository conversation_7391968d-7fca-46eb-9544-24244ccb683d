/* Luxury Jewelry Business Management System Theme */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

html,
body,
#root {
  height: 100%;
  width: 100%;
  font-family: "Inter", sans-serif;
  color: #5C5470; /* Charcoal text color */
}

.app {
  min-height: 100vh;
}

.content {
  width: 100%;
  font-family: "Inter", sans-serif;
}

.app {
  display: flex;
  position: relative;
}

::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #e0e0e0;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

a {
  text-decoration: none;
  color: inherit;
}

.cursor-pointer {
  cursor: pointer;
}

.pro-sidebar {
  position: absolute;
}

.content {
  margin-left: 270px;
  transition: margin, left, right, 0.3s;
}

.sidemenu-collapsed .content {
  margin-left: 80px;
}

#root .sidemenu-collapsed .pro-sidebar .pro-sidebar-inner .pro-sidebar-layout .scrollable-menu {
  height: calc(100vh - 100px);
}

th {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.6);
}


.dashboard-chips {
  margin-bottom: 24px;

}

.dashboard-chips .chips {
  background: #f0e9dd; /* Light gold background */
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-chips .chips:hover {
  transform: translateY(-5px);
  box-shadow: 0px 8px 25px rgba(0, 0, 0, 0.12);
}

.dashboard-chips .chips:after {
  background: #B08D57; /* Antique gold accent */
  width: 40px;
  height: 2px;
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  content: "";
}

.dashboard-chips .chips .round{
  width:100px;
  height:100px;
  position: absolute;
  top:-30px;
  right:-30px;
  border-radius: 50%;
  background: #d4c29e; /* Light gold */
}
.dashboard-chips .chips .round:after{
  content:"";
  position: absolute;
  width:80px;
  height:80px;
  top:10px;
  right:10px;
  border-radius: 50%;
  background: #B08D57; /* Antique gold */
}

.dashboard-chips .chips svg{color: #5C5470; /* Charcoal */}
.dashboard-chips .chips h5{color: #5C5470; /* Charcoal */}

.dashboard-chips .chips.second{background: #F5F5F5; /* Pearl white */}
.dashboard-chips .chips.second:after{ background: #B08D57; /* Antique gold */}
.dashboard-chips .chips.second .round{ background: #e5d8c0; /* Very light gold */}
.dashboard-chips .chips.second .round:after{ background: #B08D57; /* Antique gold */}
.dashboard-chips .chips.second svg{color: #5C5470; /* Charcoal */ }
.dashboard-chips .chips.second h5{color: #5C5470; /* Charcoal */}



.dashboard-chips .chips.third{background: #e9e7ec; /* Light charcoal */}
.dashboard-chips .chips.third:after{ background: #B08D57; /* Antique gold */}
.dashboard-chips .chips.third .round{ background: #d3d0d9; /* Very light charcoal */}
.dashboard-chips .chips.third .round:after{ background: #B08D57; /* Antique gold */}
.dashboard-chips .chips.third svg{color: #5C5470; /* Charcoal */}
.dashboard-chips .chips.third h5{color: #5C5470; /* Charcoal */}

.dashboard-chips .chips.fourth{background: #f0e9dd; /* Lightest gold */}
.dashboard-chips .chips.fourth:after{ background: #B08D57; /* Antique gold */}
.dashboard-chips .chips.fourth .round{ background: #e5d8c0; /* Very light gold */}
.dashboard-chips .chips.fourth .round:after{ background: #B08D57; /* Antique gold */}
.dashboard-chips .chips.fourth svg{color: #5C5470; /* Charcoal */}
.dashboard-chips .chips.fourth h5{color: #5C5470; /* Charcoal */}
/* Luxury Jewelry Theme Custom Styles */

/* Elegant transitions for all elements */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Custom scrollbar for luxury feel */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #F5F5F5; /* Pearl white */
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #B08D57; /* Antique gold */
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #8d7146; /* Darker gold */
}

/* Luxury card styling */
.luxury-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.luxury-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Global Luxury Button Styling */
.luxury-button {
  background-color: #B08D57; /* Antique gold */
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-transform: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.luxury-button:hover {
  background-color: #8d7146; /* Darker gold */
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* Outlined variant */
.luxury-button.outlined {
  background-color: transparent;
  color: #B08D57; /* Antique gold */
  border: 1px solid #B08D57; /* Antique gold */
}

.luxury-button.outlined:hover {
  background-color: rgba(176, 141, 87, 0.04);
  border-color: #8d7146; /* Darker gold */
  color: #8d7146; /* Darker gold */
}

/* Secondary variant (Charcoal) */
.luxury-button.secondary {
  background-color: #5C5470; /* Charcoal */
  color: #ffffff;
}

.luxury-button.secondary:hover {
  background-color: #433c53; /* Darker charcoal */
}

/* Secondary outlined variant */
.luxury-button.secondary.outlined {
  background-color: transparent;
  color: #5C5470; /* Charcoal */
  border: 1px solid #5C5470; /* Charcoal */
}

.luxury-button.secondary.outlined:hover {
  background-color: rgba(92, 84, 112, 0.04);
  border-color: #433c53; /* Darker charcoal */
  color: #433c53; /* Darker charcoal */
}

/* Danger variant */
.luxury-button.danger {
  background-color: #db4f4a; /* Red */
  color: #ffffff;
}

.luxury-button.danger:hover {
  background-color: #af3f3b; /* Darker red */
}

/* Success variant */
.luxury-button.success {
  background-color: #4cceac; /* Green */
  color: #ffffff;
}

.luxury-button.success:hover {
  background-color: #3da58a; /* Darker green */
}

/* Size variants */
.luxury-button.small {
  padding: 6px 16px;
  font-size: 12px;
}

.luxury-button.large {
  padding: 12px 24px;
  font-size: 16px;
}

/* Disabled state */
.luxury-button:disabled {
  background-color: #c0c0c0;
  color: #ffffff;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.luxury-button.outlined:disabled {
  background-color: transparent;
  color: #c0c0c0;
  border-color: #c0c0c0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Luxury input styling */
.luxury-input {
  border: 1px solid #CBD5E1;
  border-radius: 8px;
  padding: 10px 15px;
  font-family: "Inter", sans-serif;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.luxury-input:focus {
  outline: none;
  border-color: #B08D57; /* Antique gold */
  box-shadow: 0 0 0 2px rgba(176, 141, 87, 0.2);
}

/* Heading styles with Playfair Display */
.luxury-heading {
  font-family: "Playfair Display", serif;
  color: #5C5470; /* Charcoal */
  font-weight: 500;
  margin-bottom: 0.5em;
}

/* Subtle divider */
.luxury-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #B08D57, transparent);
  margin: 2rem 0;
  border: none;
}

/* Custom form label */
.luxury-label {
  font-family: "Inter", sans-serif;
  font-weight: 500;
  color: #5C5470; /* Charcoal */
  margin-bottom: 0.5rem;
  display: block;
}

/* Custom checkbox */
.luxury-checkbox {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-family: "Inter", sans-serif;
  color: #5C5470; /* Charcoal */
  display: inline-block;
  line-height: 20px;
}

.luxury-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.luxury-checkbox .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #F5F5F5; /* Pearl white */
  border: 1px solid #CBD5E1;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.luxury-checkbox:hover input ~ .checkmark {
  border-color: #B08D57; /* Antique gold */
}

.luxury-checkbox input:checked ~ .checkmark {
  background-color: #B08D57; /* Antique gold */
  border-color: #B08D57; /* Antique gold */
}

.luxury-checkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.luxury-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.luxury-checkbox .checkmark:after {
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
