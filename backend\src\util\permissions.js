const { Permissions } = require('../../lib/models/enums');

const checkPermission = (requiredPermissions) => {
    return (req, res, next) => {
        try {
            // Get user from request (set by verifyTokenAdmin middleware)
            const user = req.user;
            
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Unauthorized access'
                });
            }

            console.log('user>>', user);
            console.log('user.role>>', user.role);

            // Get role from user object - check both role property and direct role value
            const userRole = user.role || user;
            
            // If user is ADMIN, allow all actions
            if (userRole === 'ADMIN') {
                return next();
            }

            // Get role-based permissions
            const rolePermissions = getRolePermissions(userRole);

            // Check if role has required permissions
            const hasPermission = Array.isArray(requiredPermissions)
                ? requiredPermissions.every(permission => rolePermissions.includes(permission))
                : rolePermissions.includes(requiredPermissions);

            if (!hasPermission) {
                return res.status(403).json({
                    success: false,
                    message: 'Your role does not have permission to perform this action'
                });
            }

            next();
        } catch (error) {
            console.error('Permission check error:', error);
            return res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    };
};

// Define role-based permissions
const getRolePermissions = (role) => {
    if (!role) return [];
    
    const rolePermissions = {
        ADMIN: Object.values(Permissions), // Admin has all permissions
        INVENTORY_MANAGER: [
            Permissions.VIEW_INVENTORY,
            Permissions.CREATE_INVENTORY,
            Permissions.UPDATE_INVENTORY,
            Permissions.DELETE_INVENTORY,
            Permissions.MANAGE_STOCK,
            Permissions.VIEW_REPORTS
        ],
        SALES_MANAGER: [
            Permissions.VIEW_INVENTORY,
            Permissions.VIEW_ORDERS,
            Permissions.CREATE_ORDERS,
            Permissions.UPDATE_ORDERS,
            Permissions.MANAGE_ORDER_STATUS,
            Permissions.VIEW_CUSTOMERS,
            Permissions.VIEW_REPORTS
        ],
        CUSTOMER_SERVICE: [
            Permissions.VIEW_ORDERS,
            Permissions.VIEW_CUSTOMERS,
            Permissions.UPDATE_CUSTOMERS,
            Permissions.VIEW_INVENTORY
        ],
        STAFF: [
            Permissions.VIEW_INVENTORY,
            Permissions.VIEW_ORDERS,
            Permissions.VIEW_CUSTOMERS
        ]
    };

    return rolePermissions[role] || [];
};

module.exports = {
    checkPermission,
    getRolePermissions
};


