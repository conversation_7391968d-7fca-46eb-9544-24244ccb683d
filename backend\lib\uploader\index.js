const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const upload = async (file, folder = 'uploads') => {
    try {
        if (!file) {
            return {
                success: false,
                message: 'No file provided'
            };
        }

        // Create destination directory if it doesn't exist
        const uploadDir = path.join(process.cwd(), 'public', folder);
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Generate unique filename
        const ext = path.extname(file.originalname);
        const filename = `${uuidv4()}${ext}`;
        const filePath = path.join(uploadDir, filename);

        // Move file to destination
        fs.renameSync(file.path, filePath);

        // Return public URL and file path
        return {
            success: true,
            url: `/public/${folder}/${filename}`,
            path: filePath
        };
    } catch (error) {
        console.error('Error in upload:', error);
        return {
            success: false,
            message: 'Failed to upload file'
        };
    }
};

module.exports = {
    upload
};