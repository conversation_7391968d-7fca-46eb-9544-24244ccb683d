const { Response } = require('../../lib/http-response');

const validate = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body, { 
            abortEarly: false,
            stripUnknown: true 
        });
        
        if (error) {
            const errors = error.details.map(detail => detail.message);
            return res.status(400).json({
                success: false,
                message: errors[0],
                errors: errors
            });
        }
        
        next();
    };
};

module.exports = {
    validate
};