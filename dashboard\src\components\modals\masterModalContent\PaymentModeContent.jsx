import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import {
  paymentModeInitialValues,
  paymentModeValidationSchema,
} from "constants/schemas/masters";
import { useFormik } from "formik";
import { useDispatch, useSelector } from "react-redux";
import { useAddPaymentModeMutation, useEditPaymentModeMutation } from "services";
import { setShowMasterModal } from "store/slices/utilSlice";
import { useTheme } from "@mui/material";
import dataGridStyles from "../../../styles/dataGridStyles";
import { preFillValues } from "utils/common";

const PaymentModeContent = () => {
  const dispatch = useDispatch();
  // store
  const selectedModelContent = useSelector(
    (state) => state.utils.selectedModelContent
  );

  // mutations
  const [paymentModeMutation] = useAddPaymentModeMutation();
  const [editPaymentModeMutation] = useEditPaymentModeMutation();
  const theme = useTheme();
  const styles = dataGridStyles(theme.palette.mode);

  const formik = useFormik({
    initialValues: preFillValues(paymentModeInitialValues, selectedModelContent),
    validationSchema: paymentModeValidationSchema,
    onSubmit: (values) => {
      if (selectedModelContent?._id) {
        editPaymentModeMutation({
          payload: values,
          id: selectedModelContent._id,
        });
      } else {
        paymentModeMutation(values);
      }
      dispatch(setShowMasterModal(false));
    },
  });

  return (
    <Box sx={{ ...styles.formContainer, ...styles.modalContainer, }}>
      <Typography variant="h6"  gutterBottom>
        {selectedModelContent?._id ? "Edit Payment Mode" : "Add New Payment Mode"}
      </Typography>
      <form onSubmit={formik.handleSubmit}>
        <TextField
          label="Name"
          variant="outlined"
          fullWidth
          margin="normal"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.name}
          name="name"
          error={formik.touched.name && Boolean(formik.errors.name)}
          className="input"
        />
        <TextField
          type="number"
          label="Sort Order"
          variant="outlined"
          fullWidth
          margin="normal"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.sortOrder}
          name="sortOrder"
          error={formik.touched.sortOrder && Boolean(formik.errors.sortOrder)}
          className="input fieldset-1"
        />
        <Button type="submit" variant="contained" color="primary">
          Submit
        </Button>
      </form>
    </Box>
  );
};

export default PaymentModeContent;
