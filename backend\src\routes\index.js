const express = require('express');
const router = express.Router();
const fs = require('fs');
const routes = fs.readdirSync(__dirname);


routes.forEach(route => {
    if (route === 'index.js') return;
    router.use(`/${route}`, require(`./${route}`));
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.success({ status: 'ok' }, 'API is healthy');
});

module.exports = router;
