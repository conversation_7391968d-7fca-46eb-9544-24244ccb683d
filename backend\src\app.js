const express = require('express');
const cors = require('cors');
const { swagger, swaggerUi } = require('./util/swagger');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swagger));

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// ... existing code ... 