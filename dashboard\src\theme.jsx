import { createContext, useState, useMemo } from "react";
import { createTheme } from "@mui/material/styles";

// Luxury jewelry theme color palette
// Primary: Antique Gold (#B08D57)
// Secondary: <PERSON> White (#F5F5F5)
// Accent: Charcoal (#5C5470)

// color design tokens export
export const tokens = (mode) => ({
  ...(mode === "dark"
    ? {
        grey: {
          100: "#e0e0e0",
          200: "#c2c2c2",
          300: "#a3a3a3",
          400: "#858585",
          500: "#666666",
          600: "#525252",
          700: "#3d3d3d",
          800: "#292929",
          900: "#141414",
        },
        primary: {
          100: "#f0e9dd", // Lightest gold
          200: "#e5d8c0", // Very light gold
          300: "#d4c29e", // Light gold
          400: "#c4ad7d", // Medium light gold
          500: "#B08D57", // Antique gold - primary color
          600: "#8d7146", // Medium dark gold
          700: "#6a5534", // Dark gold
          800: "#483923", // Very dark gold
          900: "#251e11", // Darkest gold
        },
        greenAccent: {
          100: "#dbf5ee",
          200: "#b7ebde",
          300: "#94e2cd",
          400: "#70d8bd",
          500: "#4cceac",
          600: "#3da58a",
          700: "#2e7c67",
          800: "#1e5245",
          900: "#0f2922",
        },
        redAccent: {
          100: "#f8dcdb",
          200: "#f1b9b7",
          300: "#e99592",
          400: "#e2726e",
          500: "#db4f4a",
          600: "#af3f3b",
          700: "#832f2c",
          800: "#58201e",
          900: "#2c100f",
        },
        blueAccent: {
          100: "#e1e2fe",
          200: "#c3c6fd",
          300: "#a4a9fc",
          400: "#868dfb",
          500: "#6870fa",
          600: "#535ac8",
          700: "#3e4396",
          800: "#2a2d64",
          900: "#151632",
        },
        whiteAccent: {
          100: "#ffffff",
          200: "#f0f0f0",
          300: "#e0e0e0",
          400: "#d0d0d0",
          500: "#c0c0c0",
          600: "#b0b0b0",
          700: "#a0a0a0",
          800: "#909090",
          900: "#808080",
        },
        blackAccent:{
          100: "#F1F5F9",
          200: "#E2E8F0",
          300: "#CBD5E1",
          400: "#94A3B8",
          500: "#64748B",
          600: "#475569",
          700: "#334155",
          800: "#1E293B",
          900: "#0F172A",
        }
      }
    : {
        grey: {
          100: "#141414",
          200: "#292929",
          300: "#3d3d3d",
          400: "#525252",
          500: "#666666",
          600: "#858585",
          700: "#a3a3a3",
          800: "#c2c2c2",
          900: "#e0e0e0",
        },
        primary: {
          100: "#251e11", // Darkest gold
          200: "#483923", // Very dark gold
          300: "#6a5534", // Dark gold
          400: "#8d7146", // Medium dark gold
          500: "#B08D57", // Antique gold - primary color
          600: "#c4ad7d", // Medium light gold
          700: "#d4c29e", // Light gold
          800: "#e5d8c0", // Very light gold
          900: "#f0e9dd", // Lightest gold
        },
        greenAccent: {
          100: "#0f2922",
          200: "#1e5245",
          300: "#2e7c67",
          400: "#3da58a",
          500: "#4cceac",
          600: "#70d8bd",
          700: "#94e2cd",
          800: "#b7ebde",
          900: "#dbf5ee",
        },
        redAccent: {
          100: "#2c100f",
          200: "#58201e",
          300: "#832f2c",
          400: "#af3f3b",
          500: "#db4f4a",
          600: "#e2726e",
          700: "#e99592",
          800: "#f1b9b7",
          900: "#f8dcdb",
        },
        blueAccent: {
          100: "#151632",
          200: "#2a2d64",
          300: "#3e4396",
          400: "#535ac8",
          500: "#6870fa",
          600: "#846CF9",
          700: "#a4a9fc",
          800: "#c3c6fd",
          900: "#e1e2fe",
        },

        whiteAccent: {
          100: "#ffffff", // Pure white
          200: "#fcfcfc", // Almost white
          300: "#f9f9f9", // Very light pearl
          400: "#F5F5F5", // Pearl white - secondary color
          500: "#ebebeb", // Light pearl
          600: "#e0e0e0", // Medium pearl
          700: "#d4d4d4", // Medium dark pearl
          800: "#c9c9c9", // Dark pearl
          900: "#bdbdbd", // Darkest pearl
        },
        blackAccent:{
          100: "#e9e7ec", // Lightest charcoal
          200: "#d3d0d9", // Very light charcoal
          300: "#bdb8c6", // Light charcoal
          400: "#a7a1b3", // Medium light charcoal
          500: "#918ba0", // Medium charcoal
          600: "#7b748d", // Medium dark charcoal
          700: "#5C5470", // Charcoal - accent color
          800: "#433c53", // Very dark charcoal
          900: "#2a2535", // Darkest charcoal
        },
        goldAccent: {
          100: "#f7f1e6", // Lightest gold
          200: "#f0e3cd", // Very light gold
          300: "#e8d5b4", // Light gold
          400: "#e1c79b", // Medium light gold
          500: "#d9b982", // Medium gold
          600: "#ceaa68", // Medium dark gold
          700: "#B08D57", // Antique gold - primary color
          800: "#8d7146", // Dark gold
          900: "#6a5534"  // Darkest gold
        }

      }),
});

// mui theme settings
export const themeSettings = (mode) => {
  const colors = tokens(mode);
  return {
    palette: {
      mode: mode,
      ...(mode === "dark"
        ? {
            // palette values for dark mode
            primary: {
              main: colors.primary[500],
            },
            secondary: {
              main: colors.greenAccent[500],
            },
            neutral: {
              dark: colors.grey[700],
              main: colors.grey[500],
              light: colors.grey[100],
            },
            background: {
              default: colors.primary[500],
            },
          }
        : {
            // palette values for light mode - luxury jewelry theme
            primary: {
              main: colors.primary[500], // Antique gold
              light: colors.primary[300],
              dark: colors.primary[700],
            },
            secondary: {
              main: colors.whiteAccent[400], // Pearl white
              light: colors.whiteAccent[200],
              dark: colors.whiteAccent[600],
            },
            neutral: {
              dark: colors.blackAccent[700], // Charcoal
              main: colors.blackAccent[500],
              light: colors.blackAccent[300],
            },
            background: {
              default: colors.whiteAccent[300], // Light pearl background
              paper: colors.whiteAccent[100],
            },
            gold: {
              main: colors.goldAccent[700], // Antique gold
              light: colors.goldAccent[400],
              dark: colors.goldAccent[900],
            },
          }),
    },
    typography: {
      fontFamily: ["Inter", "sans-serif"].join(","),
      fontSize: 12,
      h1: {
        fontFamily: ["Playfair Display", "serif"].join(","),
        fontSize: 40,
        fontWeight: 500,
      },
      h2: {
        fontFamily: ["Playfair Display", "serif"].join(","),
        fontSize: 32,
        fontWeight: 500,
      },
      h3: {
        fontFamily: ["Playfair Display", "serif"].join(","),
        fontSize: 24,
        fontWeight: 500,
      },
      h4: {
        fontFamily: ["Playfair Display", "serif"].join(","),
        fontSize: 20,
        fontWeight: 500,
      },
      h5: {
        fontFamily: ["Inter", "sans-serif"].join(","),
        fontSize: 16,
        fontWeight: 500,
      },
      h6: {
        fontFamily: ["Inter", "sans-serif"].join(","),
        fontSize: 14,
        fontWeight: 500,
      },
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            boxShadow: '0px 3px 6px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            fontFamily: '"Inter", sans-serif',
            fontWeight: 500,
            fontSize: '14px',
            padding: '10px 20px',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0px 6px 10px rgba(0, 0, 0, 0.15)',
            },
            '&.MuiButton-sizeLarge': {
              padding: '12px 24px',
              fontSize: '16px',
            },
            '&.MuiButton-sizeSmall': {
              padding: '6px 16px',
              fontSize: '12px',
            },
            '&.luxury-button': {
              backgroundColor: colors.primary[500], /* Antique gold */
              color: colors.whiteAccent[100],
              '&:hover': {
                backgroundColor: colors.primary[600],
              },
              '&.outlined': {
                backgroundColor: 'transparent',
                color: colors.primary[500],
                border: `1px solid ${colors.primary[500]}`,
                '&:hover': {
                  backgroundColor: 'rgba(176, 141, 87, 0.04)',
                  borderColor: colors.primary[600],
                  color: colors.primary[600],
                },
              },
              '&.secondary': {
                backgroundColor: colors.blackAccent[700], /* Charcoal */
                color: colors.whiteAccent[100],
                '&:hover': {
                  backgroundColor: colors.blackAccent[800],
                },
              },
              '&.danger': {
                backgroundColor: colors.redAccent[500],
                color: colors.whiteAccent[100],
                '&:hover': {
                  backgroundColor: colors.redAccent[600],
                },
              },
              '&.success': {
                backgroundColor: colors.greenAccent[500],
                color: colors.whiteAccent[100],
                '&:hover': {
                  backgroundColor: colors.greenAccent[600],
                },
              },
            },
          },
          contained: {
            backgroundColor: colors.primary[500],
            color: colors.whiteAccent[100],
            '&:hover': {
              backgroundColor: colors.primary[600],
            },
          },
          outlined: {
            borderColor: colors.primary[500],
            color: colors.primary[500],
            '&:hover': {
              borderColor: colors.primary[600],
              backgroundColor: 'rgba(176, 141, 87, 0.04)',
            },
          },
        },
        defaultProps: {
          className: 'luxury-button',
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.08)',
            overflow: 'hidden',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.12)',
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              '& fieldset': {
                borderColor: colors.blackAccent[300],
                transition: 'border-color 0.3s ease',
              },
              '&:hover fieldset': {
                borderColor: colors.primary[400],
              },
              '&.Mui-focused fieldset': {
                borderColor: colors.primary[500],
              },
            },
          },
        },
      },
    },
  };
};

// context for color mode
export const ColorModeContext = createContext({
  toggleColorMode: () => {},
});

export const useMode = () => {
  const [mode, setMode] = useState("light");

  const colorMode = useMemo(
    () => ({
      toggleColorMode: () =>
        setMode((prev) => (prev === "light" ? "dark" : "light")),
    }),
    []
  );

  const theme = useMemo(() => createTheme(themeSettings(mode)), [mode]);
  return [theme, colorMode];
};
