require('custom-env').env('api');
const mongoose = require('mongoose');
const AdminSettings = require('../../lib/models/models/AdminSettings.model');

async function initAdminSettings() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        // Check if admin settings already exist
        const existingSettings = await AdminSettings.findOne();
        if (existingSettings) {
            console.log('Admin settings already exist');
            return;
        }

        // Create initial admin settings
        const adminSettings = new AdminSettings({
            androidAppVersion: '1.0.0',
            iosAppVersion: '1.0.0',
            webAppVersion: '1.0.0',
            androidForceUpdate: false,
            iosForceUpdate: false,
            webForceUpdate: false,
            maintenance: false,
            address: '',
            email: '',
            contact: '',
            codShipping: 0,
            packageWeight: 0,
            googleApiKey: '',
            googleLat: 0,
            googleLong: 0,
            commonMetaTags: [],
            metaTitle: 'StarColors BMS',
            metaDescription: 'StarColors Business Management System',
            socialLinks: new Map()
        });

        await adminSettings.save();
        console.log('Admin settings initialized successfully');
    } catch (error) {
        console.error('Error initializing admin settings:', error);
    } finally {
        await mongoose.disconnect();
    }
}

initAdminSettings(); 