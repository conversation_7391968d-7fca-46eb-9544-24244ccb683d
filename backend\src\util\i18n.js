const languages = {
    en: {
        validation: {
            any: {
                required: '{{#label}} is required',
                empty: '{{#label}} cannot be empty'
            },
            string: {
                regex: {
                    name: '{{#label}} must match the pattern {{#name}}',
                    base: '{{#label}} must be a string',
                    emailPattern: '{{#label}} must be a valid email address',
                    passwordPattern: '{{#label}} must contain at least one letter and one number, and be at least 8 characters long',
                    adminPasswordPattern: '{{#label}} must contain at least one uppercase letter, one lowercase letter, one number, one special character, no spaces, and be at least 8 characters long',
                    phonePattern: '{{#label}} must be between 4 and 15 digits',
                    countryCodePattern: '{{#label}} must start with + followed by digits',
                    otpPattern: '{{#label}} must be 6 digits'
                },
                max: '{{#label}} must be less than or equal to {{#limit}} characters long',
                min: '{{#label}} must be at least {{#limit}} characters long'
            },
            number: {
                base: '{{#label}} must be a number',
                min: '{{#label}} must be greater than or equal to {{#limit}}',
                max: '{{#label}} must be less than or equal to {{#limit}}'
            },
            objectId: {
                valid: '{{#label}} must be a valid ObjectId'
            },
            custom: {
                sameAs: (field, targetField) => `${field} must match ${targetField}`
            }
        }
    }
};

const __ = (key, params = {}) => {
    const language = 'en'; // Default to English
    const keys = key.split('.');
    let value = languages[language];
    
    for (const k of keys) {
        if (!value || !value[k]) {
            return key;
        }
        value = value[k];
    }

    return value.replace(/\{\{#(\w+)\}\}/g, (match, param) => params[param] || match);
};

module.exports = {
    __,
    languages
}; 