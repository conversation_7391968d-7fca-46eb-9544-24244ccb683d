import { API_ENDPOINTS } from "../constants/endpoints";
import { apiSlice, handlePostResponse, handleResponse } from "./base.service";

export const inventoryApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getInventories: builder.query({
      query: ({ id, params } = {}) => ({
        url: id
          ? `${API_ENDPOINTS.GET_INVENTORIES}/${id}`
          : API_ENDPOINTS.GET_INVENTORIES,
        method: "GET",
        params,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: (result, error, arg) =>
        result?.items
          ? [
              ...result.items.map(({ _id }) => ({ type: 'Inventory', id: _id })),
              { type: 'Inventory', id: 'LIST' },
            ]
          : [{ type: 'Inventory', id: arg?.id || 'LIST' }],
    }),

    getPublicInventories: builder.query({
      query: ({ id, params }) => ({
        url: id
          ? `${API_ENDPOINTS.GET_PUBLIC_INVENTORIES}/${id}`
          : API_ENDPOINTS.GET_PUBLIC_INVENTORIES,
        method: "GET",
        params,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),

    createInventory: builder.mutation({
      query: (body) => ({
        url: API_ENDPOINTS.CREATE_INVENTORY,
        method: "POST",
        body,
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),

    editInventory: builder.mutation({
      query: ({ id, body }) => ({
        url: `${API_ENDPOINTS.EDIT_INVENTORY}/${id}`,
        method: "PUT",
        body,
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),

    deleteInventory: builder.mutation({
      query: ({ id }) => ({
        url: `${API_ENDPOINTS.DELETE_INVENTORY}/${id}`,
        method: "DELETE",
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: [{ type: 'Inventory', id: 'LIST' }],
    }),
  }),
});

export const {
  useGetInventoriesQuery,
  useCreateInventoryMutation,
  useEditInventoryMutation,
  useDeleteInventoryMutation,
  useGetPublicInventoriesQuery,
} = inventoryApiSlice;
