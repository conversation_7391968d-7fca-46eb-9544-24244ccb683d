require('custom-env').env('api');
const mongoose = require('mongoose');

async function cleanup() {
    try {
        // Connect to test database
        const testUri = process.env.MONGO_URI.replace('/starcolorsbms', '/test');
        await mongoose.connect(testUri);
        console.log('Connected to test database');

        // Get all collections
        const collections = await mongoose.connection.db.collections();

        // Drop each collection
        for (const collection of collections) {
            await collection.deleteMany({});
            console.log(`Cleared collection: ${collection.collectionName}`);
        }

        await mongoose.disconnect();
        console.log('Cleanup completed successfully');

    } catch (error) {
        console.error('Error cleaning up:', error);
    }
}

cleanup(); 