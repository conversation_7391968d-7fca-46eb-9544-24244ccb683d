import React from 'react';
import { Box, TextField, Select, MenuItem, FormControl, InputLabel, FormHelperText } from '@mui/material';
import { tokens } from 'theme';
import { useTheme } from '@mui/material';

/**
 * Custom weight field component with unit selector
 * @param {Object} props
 * @param {string} props.label - Field label
 * @param {string} props.name - Field name for the value
 * @param {string} props.unitName - Field name for the unit
 * @param {string} props.value - Current value
 * @param {string} props.unit - Current unit
 * @param {function} props.onChange - Change handler for value
 * @param {function} props.onUnitChange - Change handler for unit
 * @param {function} props.onBlur - Blur handler
 * @param {boolean} props.error - Whether there's an error
 * @param {string} props.helperText - Helper text or error message
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.alphanumeric - Whether to allow alphanumeric values
 */
const WeightField = ({
  label,
  name,
  unitName,
  value,
  unit,
  onChange,
  onUnitChange,
  onBlur,
  error,
  helperText,
  placeholder,
  alphanumeric = false,
}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const UNIT_OPTIONS = [
    { value: 'ct', label: 'ct (Carat)' },
    { value: 'g', label: 'g (Gram)' },
    { value: 'mg', label: 'mg (Milligram)' },
    { value: 'kg', label: 'kg (Kilogram)' },
  ];

  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
      {/* Weight value input */}
      <TextField
        fullWidth
        variant="outlined"
        size="small"
        label={label}
        name={name}
        value={value || ''}
        onChange={onChange}
        onBlur={onBlur}
        error={error}
        helperText={helperText}
        placeholder={placeholder}
        sx={{ flex: 2 }}
      />

      {/* Unit selector */}
      <FormControl
        variant="outlined"
        size="small"
        sx={{
          flex: 1,
          minWidth: '100px',
          '& .MuiOutlinedInput-root': {
            '&:hover fieldset': {
              borderColor: colors.primary[300],
            },
            '&.Mui-focused fieldset': {
              borderColor: colors.primary[500],
            },
          },
        }}
      >
        <InputLabel>Unit</InputLabel>
        <Select
          label="Unit"
          name={unitName}
          value={unit || 'ct'}
          onChange={onUnitChange}
          onBlur={onBlur}
        >
          {UNIT_OPTIONS.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default WeightField;
