const mongoose = require('mongoose');
const { Categories } = require('../enums');
const Schema = mongoose.Schema;

// function materialCodeFromCategory(cat = '') {
//     const normalized = cat.toLowerCase();
//     return normalized === 'gold' || normalized === 'silver' ? 'AU' : 'ST';
// }

const inventorySchema = new Schema(
    {
        sku: {
            type: String,
            required: true,
            trim: true,
        },
        name: {
            type: String,

            trim: true,
        },
        description: {
            type: String,
            trim: true,
        },
        category: {
            type: String,
            enum: Categories,
        },
        type: {
            type: String,
            trim: true,
        },
        size: {
            type: String,
            trim: true,
        },
        sizes: [
            {
                value: {
                    type: String,
                    trim: true,
                },
                quantity: {
                    type: Number,
                    default: 0,
                },
            },
        ],
        weight: {
            netWt: {
                value: {
                    type: String,
                    trim: true,
                },
                unit: {
                    type: String,
                    enum: ['g', 'mg', 'ct', 'kg'],
                    default: 'ct',
                },
            },
            grossWt: {
                value: {
                    type: String,
                    trim: true,
                },
                unit: {
                    type: String,
                    enum: ['g', 'mg', 'ct', 'kg'],
                    default: 'ct',
                },
            },
            stoneWt: {
                value: {
                    type: String,
                    trim: true,
                },
                unit: {
                    type: String,
                    enum: ['g', 'mg', 'ct', 'kg'],
                    default: 'ct',
                },
            },
            diamondWt: {
                value: {
                    type: String,
                    trim: true,
                },
                unit: {
                    type: String,
                    enum: ['g', 'mg', 'ct', 'kg'],
                    default: 'ct',
                },
            },
            averageWt: {
                value: {
                    type: String,
                    trim: true,
                },
                unit: {
                    type: String,
                    enum: ['g', 'mg', 'ct', 'kg'],
                    default: 'ct',
                },
            },
        },
        purity: {
            type: String,
            trim: true,
        },
        price: {
            costPrice: {
                type: String,
                default: 0,
            },
            sellingPrice: {
                type: String,
                default: 0,
            },
            mrp: {
                type: String,
                default: '',
            },
            // Making charges
            makingCharges: {
                type: Number,
                default: 0,
            },
        },
        images: [
            {
                type: String,
            },
        ],
        stock: {
            quantity: {
                type: Number,

                default: 0,
            },
            minStockLevel: {
                type: Number,
                default: 1,
            },
        },
        qrCode: {
            type: String,
            default: '',
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
        },

        isSuspended: {
            type: Boolean,
            default: false,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        tags: [
            {
                default: [],
                type: String,
                trim: true,
            },
        ],
        status: {
            type: String,
            enum: ['available', 'sold', 'reserved', 'damaged', 'in-maintenance'],
            default: 'available',
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);
// Virtual for low stock status
inventorySchema.virtual('isLowStock').get(function () {
    return this.stock.quantity <= this.stock.minStockLevel;
});

// Index for faster searching
inventorySchema.index({ itemCode: 1, name: 1, category: 1 });
inventorySchema.index({ 'stock.quantity': 1 });

// Create a compound index for SKU and isDeleted to allow reuse of SKUs for deleted items
inventorySchema.index({ sku: 1, isDeleted: 1 }, { unique: true });

/* ---------- BUILD SKU WITHOUT material FIELD ---------- */
// async function buildSku(doc) {
//     const code = materialCodeFromCategory(doc.category);

//     // fetch highest SKU for this code
//     const latest = await mongoose.models.Inventory.find({ sku: new RegExp(`^SCI-${code}\\d{4}$`) })
//         .sort({ sku: -1 })
//         .limit(1)
//         .select({ sku: 1 })
//         .lean();

//     const lastNum = latest.length ? parseInt(latest[0].sku.slice(-4), 10) : 0;

//     const nextSeq = lastNum + 1;
//     return `SCI-${code}${nextSeq.toString().padStart(4, '0')}`;
// }

/* ---------- HOOK ---------- */
// inventorySchema.pre('validate', async function (next) {
//     if (this.sku) return next(); // manual override allowed
//     try {
//         this.sku = await buildSku(this);
//         next();
//     } catch (err) {
//         next(err);
//     }
// });

/* ---------- RETRY‑SAFE CREATOR ---------- */
// inventorySchema.statics.createWithAutoRetry = async function (attrs, tries = 3) {
//     for (let i = 1; i <= tries; i++) {
//         try {
//             return await this.create(attrs);
//         } catch (err) {
//             if (err.code === 11000 && i < tries) {
//                 // duplicate SKU
//                 delete attrs.sku; // force regeneration
//                 continue;
//             }
//             throw err;
//         }
//     }
// };

// Check if the model has already been compiled
const Inventory = mongoose.models.Inventory || mongoose.model('Inventory', inventorySchema);

module.exports = Inventory;
