const { models: { Customer } } = require('../../../lib/models');

class CustomerController {
    async listCustomers(req, res) {
        try {
            const {
                page = 1,
                limit = 10,
                search,
                sortBy = 'createdAt',
                sortOrder = 'desc'
            } = req.query;
    
            // const query = { isDeleted: false }; // Assuming you have soft delete
            const query = {};
    
            if (search) {
                const searchRegex = new RegExp(search, 'i');
                query.$or = [
                    { name: searchRegex },
                    { email: searchRegex },
                    { phone: searchRegex }
                ];
            }
    
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
    
            const [items, totalCustomers] = await Promise.all([
                Customer.find(query)
                    .sort(sortOptions)
                    .skip(skip)
                    .limit(parseInt(limit)),
                Customer.countDocuments(query)
            ]);
    
            const totalPages = Math.ceil(totalCustomers / parseInt(limit));
            const hasNextPage = page < totalPages;
            const hasPrevPage = page > 1;
    
            return res.success({
                items,
                pagination: {
                    totalItems: totalCustomers,
                    itemsPerPage: parseInt(limit),
                    totalPages,
                    currentPage: parseInt(page),
                    hasNextPage,
                    hasPrevPage
                }
            });
        } catch (error) {
            console.error('Error listing customers:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    

    async getCustomer(req, res) {
        try {
            const customer = await Customer.findById(req.params.id);
            if (!customer) {
                return res.warn({}, req.__('CUSTOMER_NOT_FOUND'));
            }
            return res.success(customer);
        } catch (error) {
            console.error('Error getting customer:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async createCustomer(req, res) {
        try {
            const newCustomer = new Customer({
                ...req.body,
                createdBy: req.user._id
            });
            const savedCustomer = await newCustomer.save();
            return res.success(savedCustomer, req.__('CUSTOMER_CREATED_SUCCESSFULLY'));
        } catch (error) {
            console.log(error);
            console.error('Error creating customer:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateCustomer(req, res) {
        try {
            const updatedCustomer = await Customer.findByIdAndUpdate(
                req.params.id,
                {
                    ...req.body,
                    updatedBy: req.user._id
                },
                { new: true }
            );
            if (!updatedCustomer) {
                return res.warn({}, req.__('CUSTOMER_NOT_FOUND'));
            }
            return res.success(updatedCustomer, req.__('CUSTOMER_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating customer:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteCustomer(req, res) {
        try {
            const deletedCustomer = await Customer.findByIdAndDelete(req.params.id);
            if (!deletedCustomer) {
                return res.warn({}, req.__('CUSTOMER_NOT_FOUND'));
            }
            return res.success({}, req.__('CUSTOMER_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting customer:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new CustomerController();