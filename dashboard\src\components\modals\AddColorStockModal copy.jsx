import {
  <PERSON><PERSON>ple<PERSON>,
  <PERSON>,
  Button,
  Modal,
  <PERSON><PERSON>ield,
  Typography,
  useTheme,
} from "@mui/material";
import { useState } from "react";
import dataGridStyles from "../../styles/dataGridStyles";
import FileUploadProgress from "components/FileUploadProgress";
import { handleFileUpload } from "utils/common";

const AddColorStockModal = ({ formik, show, setShow, colorsData }) => {
  const theme = useTheme();
  const styles = dataGridStyles(theme.palette.mode);
  const [uploadProgress, setUploadProgress] = useState({
    mainImage: 0,
    images: 0,
  });
  const [colorObj, setColorObj] = useState({
    name: "",
    hexCode: "",
    images: {
      mainImage: "",
      images: [],
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    formik.setFieldValue("color", [...formik.values.color, colorObj]);
    setShow(false);
    setColorObj({
      name: "",
      hexCode: "",
      images: {
        mainImage: "",
        images: [],
      },
    });
  };

  const handleFileChange = (evt, type) => {
    const files = Array.from(evt.target.files);
    const newFiles = files.map((file) => {
      const fileName = `${Date.now()}_${file.name}`;
      return new File([file], fileName, { type: file.type });
    });

    newFiles.forEach((newFile) => {
      handleFileUpload(
        newFile,
        "products",
        (progress) => {
          setUploadProgress((prevUploadProgress) => ({
            ...prevUploadProgress,
            [type]: progress,
          }));
        },
        (url) => {
          if (type === "mainImage" && url) {
            setColorObj((prevColorObj) => ({
              ...prevColorObj,
              images: {
                ...prevColorObj.images,
                mainImage: url || "",
              },
            }));
          } else if (type === "images" && url) {
            setColorObj((prevColorObj) => ({
              ...prevColorObj,
              images: {
                ...prevColorObj.images,
                images: [...prevColorObj.images.images, url],
              },
            }));
          }
        }
      );
    });
  };

  const handleRemoveImage = (idx) => {
    const newImages = [...colorObj.images.images];
    newImages.splice(idx, 1);
    setColorObj({
      ...colorObj,
      images: {
        ...colorObj.images,
        images: newImages,
      },
    });
  };

  return (
    <Modal open={show} onClose={() => setShow(false)}>
      <Box sx={{ ...styles.formContainer, ...styles.modalContainer }}>
        <Typography variant="h6">Add Color Data</Typography>
        <form onSubmit={handleSubmit}>
          <Autocomplete
            options={colorsData}
            getOptionLabel={(option) => option.name}
            onBlur={formik.handleBlur}
            onChange={(_, value) =>
              setColorObj({
                ...colorObj,
                name: value?.name,
                hexCode: value?.hexCode,
              })
            }
            name="color"
            sx={{ gridColumn: "span 2", my: 1 }}
            renderInput={(params) => (
              <TextField
                {...params}
                fullWidth
                variant="filled"
                label="Color"
                InputLabelProps={{ shrink: true }}
              />
            )}
            renderOption={(props, option) => {
              const isDisabled = formik.values.color.some(
                (colorItem) => colorItem.name === option.name
              );
              return (
                <li
                  {...props}
                  style={{
                    pointerEvents: isDisabled ? "none" : "auto",
                    opacity: isDisabled ? 0.5 : 1,
                  }}
                >
                  {option?.name}
                </li>
              );
            }}
          />
          <TextField
            fullWidth
            variant="filled"
            type="file"
            onChange={(evt) => handleFileChange(evt, "mainImage")}
            name="mainImage"
            sx={{ gridColumn: "span 2", my: 1 }}
            className="input"
            InputProps={{
              accept: "image/*",
              endAdornment: (
                <FileUploadProgress progress={uploadProgress?.mainImage} />
              ),
            }}
            InputLabelProps={{ shrink: true }}
            label="Main Image"
          />
          {colorObj?.images?.mainImage && (
            <img
              src={colorObj?.images?.mainImage}
              alt="product"
              style={{ width: "100px", height: "100px" }}
            />
          )}
          <TextField
            fullWidth
            variant="filled"
            type="file"
            onChange={(evt) => handleFileChange(evt, "images")}
            name="image"
            sx={{ gridColumn: "span 2", my: 1 }}
            className="input"
            InputProps={{
              accept: "image/*",
              endAdornment: (
                <FileUploadProgress progress={uploadProgress?.images} />
              ),
            }}
            InputLabelProps={{ shrink: true }}
            label="Images"
            inputProps={{
              multiple: true,
            }}
          />

          <Box
            sx={{
              gridColumn: "span 2",
              display: "flex",
              gap: 2,
              overflow: "auto",
              flexWrap: "wrap",
            }}
          >
            {colorObj?.images?.images?.length > 0 &&
              colorObj.images.images.map((image, idx) => {
                return (
                  <Box
                    key={idx}
                    sx={{
                      position: "relative",
                      width: { xs: "100px", sm: "100px", md: "150px" }, // Responsive width
                      height: { xs: "100px", sm: "100px", md: "150px" }, // Responsive height
                      marginBottom: 2,
                    }}
                  >
                    <Button
                      className="cursor-pointer"
                      onClick={() => handleRemoveImage(idx)}
                      sx={{
                        position: "absolute",
                        top: 4,
                        right: 4,
                        background: "rgba(255, 255, 255, 0.8)",
                        borderRadius: "50%",
                        minWidth: "auto",
                        padding: 0,
                        width: 24,
                        height: 24,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      X
                    </Button>
                    <img
                      src={image}
                      alt="product"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        borderRadius: 4,
                      }}
                    />
                  </Box>
                );
              })}
          </Box>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
          >
            Submit
          </Button>
          <Button
            variant="contained"
            color="secondary"
            sx={{ mt: 2, ml: 2 }}
            onClick={() => setShow(false)}
          >
            Close
          </Button>
        </form>
      </Box>
    </Modal>
  );
};

export default AddColorStockModal;
