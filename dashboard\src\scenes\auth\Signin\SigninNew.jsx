import React, { useState } from "react";
import {
  Box,
  <PERSON><PERSON>,
  Container,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
  Card,
} from "@mui/material";
import {
  Visibility,
  VisibilityOff,
  LockOutlined,
  EmailOutlined,
} from "@mui/icons-material";
import { useFormik } from "formik";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useSignInMutation } from "services";
import { setUserData } from "store/slices/authSlice";
import { signInInitialValues, signInValidationSchema } from "constants/schemas";
import { tokens } from "../../../theme";
import { useTheme } from "@mui/material";
import "./SignIn.css";
import StarColorsLogo from "../../../assets/starcolorslogo.svg";
import { StarColorLogo, Starcolorbmslogo } from '../../../utils/images';

const SignIn = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [signIn] = useSignInMutation();

  const formik = useFormik({
    initialValues: signInInitialValues,
    validationSchema: signInValidationSchema,
    onSubmit: (values) => {
      signIn(values)
        .unwrap()
        .then((result) => {
          if (result?.token) {
            dispatch(setUserData(result));
            navigate("/");
          }
        });
    },
  });

  return (
    <Box className="signin-root" style={{
      background: `linear-gradient(135deg, ${colors.primary[900]} 0%, ${colors.blackAccent[700]} 100%)`,
    }}>
      <div className="background-blur top-blur" style={{ background: `radial-gradient(circle, ${colors.primary[400]} 0%, transparent 70%)` }} />
      <div className="background-blur bottom-blur" style={{ background: `radial-gradient(circle, ${colors.primary[500]} 0%, transparent 70%)` }} />

      <Container maxWidth="lg" className="signin-container">
        <Card className="glossy-card">
          {/* Left Panel */}
          <Box className="image-panel">
            <Box className="jewelry-image">
              <Box className="logo-container">
                {/* <img src={StarColorsLogo} alt="Star Colors Logo" className="logo-img" /> */}
               
                <Starcolorbmslogo />

                <Typography variant="h3" className="logo-title">
                  Star Colors Inc.
                </Typography>
                <Typography variant="h6" className="logo-subtitle">
                  Luxury Jewelry Management System
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Right Panel */}
          <Box className="form-panel">
            <Box className="mobile-logo">
            <Starcolorbmslogo />

              <Typography variant="h4" className="mobile-title">
                Star Colors Inc.
              </Typography>
            </Box>

            <Typography variant="h3" className="welcome-title">
              Welcome Back
            </Typography>
            <Typography variant="body1" className="welcome-subtitle">
              Sign in to access your jewelry management dashboard
            </Typography>

            <Box component="form" onSubmit={formik.handleSubmit}>
              <TextField
                fullWidth
                margin="normal"
                id="email"
                name="email"
                label="Email Address"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailOutlined sx={{ color: colors.primary[500] }} />
                    </InputAdornment>
                  ),
                }}
                className="styled-textfield"
              />

              <TextField
                fullWidth
                margin="normal"
                id="password"
                name="password"
                label="Password"
                type={showPassword ? "text" : "password"}
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.password && Boolean(formik.errors.password)}
                helperText={formik.touched.password && formik.errors.password}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockOutlined sx={{ color: colors.primary[500] }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                        sx={{ color: colors.primary[500] }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                className="styled-textfield"
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                className="shimmer-button"
              >
                Sign In
              </Button>

              <Box className="footer">
                <Typography variant="body2">
                  {import.meta.env.VITE_APP_NAME || 'Star Colors Inc.'} © {new Date().getFullYear()}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Card>
      </Container>
    </Box>
  );
};

export default SignIn;
