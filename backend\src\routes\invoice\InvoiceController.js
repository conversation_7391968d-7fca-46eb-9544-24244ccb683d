const {
    models: { Invoice, Customer },
} = require('../../../lib/models');
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');
const path = require('path');
const fs = require('fs');

class InvoiceController {
    async getInvoices(req, res) {
        try {
            const { page = 1, limit = 10, status, search } = req.query;

            let query = {};
            if (status) query.status = status;
            if (search) {
                query.$or = [{ invoiceNumber: new RegExp(search, 'i') }, { 'customer.name': new RegExp(search, 'i') }];
            }

            const invoices = await Invoice.find(query)
                .populate('customer', 'name email')
                // .populate('orderId', 'orderNumber')
                .skip((page - 1) * limit)
                .limit(limit)
                .sort({ createdAt: -1 });

            const total = await Invoice.countDocuments(query);

            return res.success({
                invoices,
                total,
                pages: Math.ceil(total / limit),
            });
        } catch (error) {
            console.error('Error fetching invoices:', error);
            return res.serverError(error.message);
        }
    }

    async getInvoice(req, res) {
        try {
            const { id } = req.params;

            const invoice = await Invoice.findById(id)
                .populate('customer', 'name email')
                // .populate('orderId', 'orderNumber')
                .populate('items.product');

            if (!invoice) {
                return res.notFound('Invoice not found');
            }

            return res.success({ invoice });
        } catch (error) {
            console.error('Error fetching invoice:', error);
            return res.serverError(error.message);
        }
    }

    async createInvoice(req, res) {
        try {
            const { customer, items, discount = 0, paymentMethod } = req.body;

            if (!customer || !items || !items.length) {
                return res
                    .status(400)
                    .json({ success: false, message: 'Customer and at least one item are required.' });
            }

            // Calculate totals
            let subtotal = 0;
            let gstTotal = 0;

            for (const item of items) {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;
                if (item.gst?.amount) {
                    gstTotal += item.gst.amount;
                }

                // 🔥 NEW: Fetch product name if product ID exists
                if (item.product && !item.name) {
                    const product = await Product.findById(item.product).select('name');
                    if (product) {
                        item.name = product.name;
                    } else {
                        item.name = 'Unnamed Product'; // fallback just in case
                    }
                }
            }

            const total = subtotal + gstTotal - discount;

            // Create the invoice
            const invoice = new Invoice({
                customer,
                items,
                subtotal,
                discount,
                gstTotal,
                total,
                paymentMethod,
                createdBy: req.user._id,
            });

            await invoice.save();

            return res.status(201).json({
                success: true,
                message: 'Invoice created successfully',
                invoiceId: invoice._id,
                invoiceNumber: invoice.invoiceNumber,
            });
        } catch (error) {
            console.error('Error creating invoice:', error);
            return res.error(error.message);
        }
    }

    async generateInvoice(req, res) {
        try {
            const { id } = req.params;
    
            // Find the invoice and populate the related customer and items
            const invoice = await Invoice.findById(id).populate('customer').populate('items.product');
    
            if (!invoice) {
                return res.notFound('Invoice not found');
            }
    
            // Create PDF document
            const doc = new PDFDocument();
            const buffers = [];
    
            doc.on('data', buffers.push.bind(buffers));
            doc.on('end', () => {
                const pdfData = Buffer.concat(buffers);
                res.writeHead(200, {
                    'Content-Length': Buffer.byteLength(pdfData),
                    'Content-Type': 'application/pdf',
                    'Content-Disposition': `attachment;filename=invoice-${invoice.invoiceNumber}.pdf`,
                });
                res.end(pdfData);
            });
    
            // Add logo (optional)
            doc.image(path.join(__dirname, '../../../public/assets/logo.png'), 50, 45, { width: 50 });
    
            // Invoice Header
            doc.fontSize(20).text('INVOICE', 50, 50);
            doc.fontSize(10).text(`Invoice Number: ${invoice.invoiceNumber}`, 50, 100);
            doc.text(`Date: ${new Date(invoice.createdAt).toLocaleDateString()}`, 50, 115);
    
            // Customer Info
            doc.text(`Customer: ${invoice.customer?.name || 'Unnamed Customer'}`, 50, 150);
            doc.text(`Email: ${invoice.customer?.email || 'N/A'}`, 50, 165);
    
            // Items Table
            let y = 200;
            doc.fontSize(12).text('Item', 50, y);
            doc.text('Qty', 200, y);
            doc.text('Price', 300, y);
            doc.text('Total', 400, y);
    
            y += 20;
            invoice.items.forEach(item => {
                doc.text(item.product?.name || item.name, 50, y);
                doc.text(item.quantity.toString(), 200, y);
                doc.text(item.price.toFixed(2), 300, y);
                doc.text((item.quantity * item.price).toFixed(2), 400, y);
                y += 20;
            });
    
            // Totals
            y += 20;
            doc.fontSize(10);
    
            // Ensure valid numbers before calling .toFixed()
            const subtotal = invoice.subtotal || 0;
            const discount = invoice.discount || 0;
            const gstTotal = invoice.gstTotal || 0;
            const total = invoice.total || 0;
    
            doc.text(`Subtotal: ₹${subtotal.toFixed(2)}`, 300, y);
            y += 15;
            doc.text(`Discount: ₹${discount.toFixed(2)}`, 300, y);
            y += 15;
            doc.text(`GST: ₹${gstTotal.toFixed(2)}`, 300, y);
            y += 15;
            doc.text(`Total: ₹${total.toFixed(2)}`, 300, y);
    
            doc.end();
    
            // Update the invoice status if needed
            if (invoice.status !== 'generated') {
                invoice.status = 'generated';
                await invoice.save();
            }
        } catch (error) {
            console.error('Error generating invoice PDF:', error);
            return res.serverError(error.message);
        }
    }
    

    async downloadInvoice(req, res) {
        try {
            const { id } = req.params;
            
            // Retrieve invoice by ID and populate related fields
            const invoice = await Invoice.findById(id)
                .populate('customer')
                .populate('items.product');
    
            if (!invoice) {
                return res.notFound('Invoice not found');
            }
    
            if (invoice.status !== 'generated') {
                return res.badRequest('Invoice has not been generated yet');
            }
    
            // Create PDF document
            const doc = new PDFDocument();
            const buffers = [];
            
            doc.on('data', buffers.push.bind(buffers));
            doc.on('end', () => {
                const pdfData = Buffer.concat(buffers);
                res.writeHead(200, {
                    'Content-Length': Buffer.byteLength(pdfData),
                    'Content-Type': 'application/pdf',
                    'Content-Disposition': `attachment;filename=invoice-${invoice.invoiceNumber}.pdf`,
                });
                res.end(pdfData);
            });
    
            // Add company logo
            doc.image(path.join(__dirname, '../../../public/assets/logo.png'), 50, 45, { width: 50 });
    
            // Add invoice details
            doc.fontSize(20).text('INVOICE', 50, 50);
            doc.fontSize(10).text(`Invoice Number: ${invoice.invoiceNumber}`, 50, 100);
            doc.text(`Date: ${new Date(invoice.createdAt).toLocaleDateString()}`, 50, 115);
    
            // Add customer details
            doc.text(`Customer: ${invoice.customer?.name || 'Unnamed Customer'}`, 50, 150);
            doc.text(`Email: ${invoice.customer?.email || 'N/A'}`, 50, 165);
    
            // Add items table
            let y = 200;
            doc.text('Item', 50, y);
            doc.text('Quantity', 200, y);
            doc.text('Price', 300, y);
            doc.text('Total', 400, y);
    
            y += 20;
            invoice.items.forEach(item => {
                doc.text(item.product?.name || item.name, 50, y);
                doc.text(item.quantity.toString(), 200, y);
                doc.text(item.price.toFixed(2), 300, y); // Ensure price is a valid number
                doc.text((item.quantity * item.price).toFixed(2), 400, y); // Ensure total is calculated correctly
                y += 20;
            });
    
            // Add totals with safe checks for undefined values
            y += 20;
            doc.fontSize(10);
    
            // Ensure safe values for subtotal, gstTotal, and total
            const subtotal = invoice.subtotal || 0;
            const gstTotal = invoice.gstTotal || 0;
            const total = invoice.total || 0;
    
            doc.text(`Subtotal: ₹${subtotal.toFixed(2)}`, 300, y);
            y += 15;
            doc.text(`GST: ₹${gstTotal.toFixed(2)}`, 300, y);
            y += 15;
            doc.text(`Total: ₹${total.toFixed(2)}`, 300, y);
    
            doc.end();
        } catch (error) {
            console.error('Error downloading invoice:', error);
            return res.serverError(error.message);
        }
    }
    
}

module.exports = new InvoiceController();
