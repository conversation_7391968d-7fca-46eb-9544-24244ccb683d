const axios = require('axios');
const { sendMail } = require('../../../lib/mailer');
const {
    models: { ContactUs, User, Product, Order },
} = require('../../../lib/models');
const { upload } = require('../../../lib/uploader');
const path = require('path');
const fs = require('fs');
const { logError } = require('../../../lib/util');

class UtilController {
    async uploadFile(req, res) {
        const { location, type, count = 1 } = req.query;
        const extensions = { IMAGE: 'jpg', 'DOCUMENT.PDF': 'pdf' };
        const extension = extensions[type] || '';
        if (!extension) return res.warn('', req.__('INVALID_FILE_TYPE'));

        const uploader = require('../../../../lib/uploader');
        const promises = [];
        for (let i = 1; i <= count; i++) {
            promises.push(uploader.getSignedUrl(location.endsWith('/') ? location : `${location}/`, extension));
        }

        const urls = await Promise.all(promises);
        return res.success(urls);
    }

    async uploadMedia(req, res) {
        try {
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: 'No file uploaded'
                });
            }

            const result = await upload(req.file, 'media');
            
            if (!result.success) {
                return res.status(400).json({
                    success: false,
                    message: result.message || 'Failed to upload file'
                });
            }

            return res.json({
                success: true,
                data: {
                    url: result.url,
                    path: result.path
                }
            });
        } catch (error) {
            logError('Error in uploadMedia:', error);
            return res.status(500).json({
                success: false,
                message: 'GENERAL_ERROR'
            });
        }
    }

    async contactUs(req, res) {
        const { name, email, message, type } = req.body;

        try {
            // Save contact form data to MongoDB
            const newContact = new ContactUs({
                name,
                email,
                message,
                type,
            });

            await newContact.save();
            // Prepare email data
            const emailData = {
                name,
                email,
                message,
                type,
            };
            // Send email using the sendMail function
            await sendMail('contact-template', 'New Contact Us Message', email, emailData);

            return res.success({ message: 'Our team will reach you soon, your message sent successfully' });
        } catch (error) {
            console.error('Error sending contact us email:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async dashboard(req, res) {
        try {
            // Initialize the dashboard data with default values
            const dashboardData = {
                ordersCount: 0,
                usersCount: 0,
                productsCount: 0,
                todaysCollection: 0,
                pendingOrders: [],
                cancelOrders: [],
            };

            // Get users count from MongoDB
            dashboardData.usersCount = await User.countDocuments({ isDeleted: false });

            // Get products count from MongoDB
            dashboardData.productsCount = await Product.countDocuments({ isDeleted: false });

            // Get orders count from MongoDB
            dashboardData.ordersCount = await Order.countDocuments({});

            // Get today's collection (sum of all paid orders created today)
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            dashboardData.todaysCollection = await Order.aggregate([
                {
                    $match: {
                        createdAt: { $gte: today },
                        status: 'Delivered',
                    },
                },
                {
                    $group: {
                        _id: null,
                        total: { $sum: '$totalPrice' },
                    },
                },
                {
                    $project: { _id: 0, total: 1 },
                },
            ]).then(res => res[0]?.total || 0);

            // Get pending orders (status is 'Pending')
            dashboardData.pendingOrders = await Order.find({ status: 'Pending' })
                .select('orderId products totalPrice createdAt')
                .populate('user', 'email fullName fName lName')
                .populate('products.productId', 'name mainImage')
                .sort({ createdAt: -1 })
                .limit(5);

            // Get canceled orders (status is 'Cancelled')
            dashboardData.cancelOrders = await Order.find({ status: 'Cancelled' })
                .select('orderId products totalPrice createdAt')
                .populate('user', 'email fullName fName lName')
                .populate('products.productId', 'name mainImage')
                .sort({ createdAt: -1 })
                .limit(5);

            // Respond with the dashboard data
            return res.json(dashboardData);
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

}
module.exports = new UtilController();
