const { Joi } = require('../../util/validations');

const requireSlug = Joi.object().keys({
    slug: Joi.string()
        .required(),
});

const requireId = Joi.object().keys({
    id: Joi.string()
        .required()
        .regex(/^[0-9a-fA-F]{24}$/)
        .messages({
            'string.pattern.base': 'Invalid ID format',
            'any.required': 'ID is required'
        })
});

// Validation for gold and silver items
const goldSilverInventoryValidation = Joi.object().keys({
    category: Joi.string()
        .valid('gold', 'silver')
        .required(),
    purity: Joi.string()
        .valid('ss', '14', '18', '20', '22')
        .required(),
    weight: Joi.object().keys({
        grossweight: Joi.object().keys({
            value: Joi.number().required(),
            unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('g')
        }),
        netweight: Joi.object().keys({
            value: Joi.number().required(),
            unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('g')
        }),
        stoneweight: Joi.object().keys({
            value: Joi.number().required(),
            unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('ct')
        }),
        diamondweight: Joi.object().keys({
            value: Joi.number().required(),
            unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('ct')
        })
    }),
    sku: Joi.string().required(),
    size: Joi.string().required(),
    price: Joi.object().keys({
        costPrice: Joi.number().required(),
        sellingPrice: Joi.number().required()
    }),
    name: Joi.string().required(),
    description: Joi.string().allow('', null)
});

// Validation for other categories
const otherInventoryValidation = Joi.object().keys({
    category: Joi.string()
        .valid('diamond', 'platinum', 'gemstone', 'pearl', 'other', 'beats')
        .required(),
    name: Joi.string().required(),
    weight: Joi.object().keys({
        grossweight: Joi.object().keys({
            value: Joi.number().required(),
            unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('g')
        })
    }),
    size: Joi.string().required(),
    price: Joi.object().keys({
        costPrice: Joi.number().required(),
        sellingPrice: Joi.number().required()
    }),
    sku: Joi.string().required(),
    description: Joi.string().allow('', null)
});

// Combined validation that checks category and applies appropriate validation
const inventoryValidation = Joi.object().keys({
    category: Joi.string()
        .valid('gold', 'silver', 'diamond', 'platinum', 'gemstone', 'pearl', 'other', 'beats')
        .required(),
    purity: Joi.when('category', {
        is: Joi.string().valid('gold', 'silver'),
        then: Joi.string()
            .valid('ss', '14', '18', '20', '22')
            .required(),
        otherwise: Joi.string()
            .valid('ss', '14', '18', '20', '22')
            .optional()
    }),
    weight: Joi.when('category', {
        is: Joi.string().valid('gold', 'silver'),
        then: Joi.object({
            grossweight: Joi.object({
                value: Joi.number().required(),
                unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('g')
            }).required(),
            netweight: Joi.object({
                value: Joi.number().required(),
                unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('g')
            }).required(),
            stoneweight: Joi.object({
                value: Joi.number().required(),
                unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('ct')
            }).required(),
            diamondweight: Joi.object({
                value: Joi.number().required(),
                unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('ct')
            }).required()
        }).required(),
        otherwise: Joi.object({
            grossweight: Joi.object({
                value: Joi.number().required(),
                unit: Joi.string().valid('g', 'mg', 'ct', 'kg').default('g')
            }).required()
        }).required()
    }),
    sku: Joi.string().required(),
    size: Joi.string().required(),
    price: Joi.object({
        costPrice: Joi.number().required(),
        sellingPrice: Joi.number().required(),
        mrp: Joi.number().optional(),
        makingCharges: Joi.number().default(0)
    }).required(),
    name: Joi.string().required(),
    description: Joi.string().allow('', null),
    type: Joi.string(),
    images: Joi.array().items(Joi.string()),
    stock: Joi.object({
        quantity: Joi.number().default(0),
        minStockLevel: Joi.number().default(1)
    }),
    warehouse: Joi.string().regex(/^[0-9a-fA-F]{24}$/),
    location: Joi.string(),
    qrCode: Joi.string(),
    hasGST: Joi.boolean().default(true),
    gstRate: Joi.number().default(3),
    supplier: Joi.string().regex(/^[0-9a-fA-F]{24}$/),
    tags: Joi.array().items(Joi.string()),
    status: Joi.string().valid('available', 'sold', 'reserved', 'damaged', 'in-maintenance').default('available'),
    notes: Joi.string()
});

module.exports = {
    requireSlug,
    goldSilverInventoryValidation,
    otherInventoryValidation,
    inventoryValidation,
    requireId
};
