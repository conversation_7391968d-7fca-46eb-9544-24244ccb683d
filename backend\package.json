{"name": "starcolors-bms", "version": "0.1.0", "description": "StarColors Business Management System", "private": true, "repository": {"type": "git", "url": "https://gitlab.com/RonakSethi/startcolorsbms.git"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "main": "src/index.js", "scripts": {"start": "nodemon src/index.js", "dev": "nodemon src/index.js", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "format": "prettier --write \"**/*.{js,json,md}\"", "test": "jest", "deploy": "firebase deploy --only functions", "prepare": "if [ \"$CI\" != \"true\" ]; then husky install; fi"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "aws-sdk": "^2.618.0", "bcrypt": "^5.1.1", "compare-versions": "^6.1.1", "compression": "^1.7.4", "connect-mongo": "^3.0.0", "cors": "^2.8.5", "custom-env": "^2.0.6", "dotenv": "^8.1.0", "ejs-locals": "^1.0.2", "email-templates": "^12.0.2", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.1.7-alpha.3", "express-mung": "^0.5.1", "express-session": "^1.17.0", "fcm-node": "^1.6.1", "firebase-admin": "^12.2.0", "firebase-functions": "^5.0.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.24.0", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "path": "^0.12.7", "pdfkit": "^0.17.0", "qrcode": "^1.5.4", "razorpay": "^2.9.4", "redis": "^4.7.0", "short-unique-id": "^5.2.2", "slugify": "^1.6.6", "socket.io": "^2.5.1", "stripe": "^8.68.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.1.4", "uuid": "^9.0.0", "yamljs": "^0.3.0"}, "devDependencies": {"@types/node": "^12.7.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "jest": "^29.7.0", "lint-staged": "^15.2.2", "morgan": "^1.9.1", "nodemon": "^3.1.0", "prettier": "^3.2.5", "supertest": "^6.3.4"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}