import React from 'react';
import { 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  FormHelperText,
  Box,
  Typography,
  useTheme
} from '@mui/material';
import { tokens } from '../theme';

const JewelryFormField = ({
  type = 'text',
  label,
  name,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  options = [],
  required = false,
  disabled = false,
  fullWidth = true,
  multiline = false,
  rows = 4,
  placeholder,
  ...props
}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const renderField = () => {
    switch (type) {
      case 'select':
        return (
          <FormControl 
            fullWidth={fullWidth} 
            error={!!error} 
            required={required}
            disabled={disabled}
          >
            <InputLabel 
              id={`${name}-label`}
              sx={{
                color: colors.blackAccent[600],
                '&.Mui-focused': {
                  color: colors.primary[500],
                }
              }}
            >
              {label}
            </InputLabel>
            <Select
              labelId={`${name}-label`}
              id={name}
              name={name}
              value={value || ''}
              onChange={onChange}
              onBlur={onBlur}
              label={label}
              sx={{
                borderRadius: '8px',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: colors.blackAccent[300],
                  transition: 'border-color 0.3s ease',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: colors.primary[400],
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: colors.primary[500],
                  borderWidth: '1px',
                },
              }}
              {...props}
            >
              {options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {error && <FormHelperText>{helperText}</FormHelperText>}
          </FormControl>
        );
      
      case 'textarea':
        return (
          <TextField
            fullWidth={fullWidth}
            id={name}
            name={name}
            label={label}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            error={!!error}
            helperText={error ? helperText : ''}
            required={required}
            disabled={disabled}
            multiline
            rows={rows}
            placeholder={placeholder}
            InputLabelProps={{ 
              shrink: true,
              sx: {
                color: colors.blackAccent[600],
                '&.Mui-focused': {
                  color: colors.primary[500],
                }
              }
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                '& fieldset': {
                  borderColor: colors.blackAccent[300],
                  transition: 'border-color 0.3s ease',
                },
                '&:hover fieldset': {
                  borderColor: colors.primary[400],
                },
                '&.Mui-focused fieldset': {
                  borderColor: colors.primary[500],
                  borderWidth: '1px',
                },
              }
            }}
            {...props}
          />
        );
      
      default:
        return (
          <TextField
            fullWidth={fullWidth}
            id={name}
            name={name}
            label={label}
            type={type}
            value={value || ''}
            onChange={onChange}
            onBlur={onBlur}
            error={!!error}
            helperText={error ? helperText : ''}
            required={required}
            disabled={disabled}
            multiline={multiline}
            rows={multiline ? rows : undefined}
            placeholder={placeholder}
            InputLabelProps={{ 
              shrink: true,
              sx: {
                color: colors.blackAccent[600],
                '&.Mui-focused': {
                  color: colors.primary[500],
                }
              }
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                '& fieldset': {
                  borderColor: colors.blackAccent[300],
                  transition: 'border-color 0.3s ease',
                },
                '&:hover fieldset': {
                  borderColor: colors.primary[400],
                },
                '&.Mui-focused fieldset': {
                  borderColor: colors.primary[500],
                  borderWidth: '1px',
                },
              }
            }}
            {...props}
          />
        );
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      {renderField()}
    </Box>
  );
};

export default JewelryFormField;
