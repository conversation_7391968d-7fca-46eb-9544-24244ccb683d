const express = require('express');
const router = express.Router();
const UtilController = require('./UtilController');
const validations = require('./UtilValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');
const upload = require('../../../lib/uploader/multer');

router.get('/upload-file', verifyToken, validate(validations.uploadFile, 'query'), UtilController.uploadFile);
router.post('/contact-us', UtilController.contactUs);
router.get('/dashboard', verifyTokenUserOrAdmin, UtilController.dashboard);

// Updated media upload route - validate after file upload
router.post('/upload-media',
    verifyToken,
    upload.single('media'),
    validate(validations.uploadMedia, 'body'),
    UtilController.uploadMedia
);

module.exports = router;
