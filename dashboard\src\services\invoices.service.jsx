import { API_ENDPOINTS } from "../constants/endpoints";
import { apiSlice, handlePostResponse, handleResponse } from "./base.service";

export const invoicesApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getInvoices: builder.query({
      query: ({ id, params } = {}) => ({
        url: id
          ? `${API_ENDPOINTS.GET_INVOICES}/${id}`
          : API_ENDPOINTS.GET_INVOICES,
        method: "GET",
        params,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    createInvoice: builder.mutation({
      query: (payload) => ({
        url: API_ENDPOINTS.CREATE_INVOICE,
        method: "POST",
        body: payload,
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),
    updateInvoice: builder.mutation({
      query: ({ payload, invoiceId }) => ({
        url: `${API_ENDPOINTS.UPDATE_INVOICE}/${invoiceId}`,
        method: "PUT",
        body: payload,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    generateInvoice: builder.mutation({
      query: ({ invoiceId }) => ({
        url: `${API_ENDPOINTS.GET_INVOICES}/${invoiceId}/generate`,
        method: "POST",
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),
    downloadInvoice: builder.mutation({
      query: ({ invoiceId }) => ({
        url: `${API_ENDPOINTS.GET_INVOICES}/${invoiceId}/download`,
        method: "GET",
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),
  }),
});

export const {
  useGetInvoicesQuery,
  useCreateInvoiceMutation,
  useUpdateInvoiceMutation,
  useGenerateInvoiceMutation,
  useDownloadInvoiceMutation,
} = invoicesApiSlice;
