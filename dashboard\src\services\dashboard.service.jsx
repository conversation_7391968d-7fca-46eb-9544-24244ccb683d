import { API_ENDPOINTS } from "../constants/endpoints";
import { apiSlice, handleResponse } from "./base.service";

export const dashboardApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getDashboardInfo: builder.query({
      query: () => ({
        url: API_ENDPOINTS.GET_DASHBOARD_INFO,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["Dashboard"],
    }),
    
    getInventoryStats: builder.query({
      query: () => ({
        url: `${API_ENDPOINTS.GET_INVENTORIES}/stats`,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["InventoryStats"],
    }),
    
    getRecentInventory: builder.query({
      query: () => ({
        url: API_ENDPOINTS.GET_INVENTORIES,
        params: {
          page: 1,
          limit: 5,
          sortBy: "created",
          sortOrder: "desc",
        },
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["RecentInventory"],
    }),
    
    getLowStockItems: builder.query({
      query: () => ({
        url: API_ENDPOINTS.GET_INVENTORIES,
        params: {
          lowStock: true,
          page: 1,
          limit: 5,
        },
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["LowStockItems"],
    }),
    
    getRecentInvoices: builder.query({
      query: () => ({
        url: API_ENDPOINTS.GET_INVOICES,
        params: {
          page: 1,
          limit: 5,
          sortBy: "createdAt",
          sortOrder: "desc",
        },
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["RecentInvoices"],
    }),
    
    getCategoryDistribution: builder.query({
      query: () => ({
        url: `${API_ENDPOINTS.GET_INVENTORIES}/categories`,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["CategoryDistribution"],
    }),
  }),
});

export const {
  useGetDashboardInfoQuery,
  useGetInventoryStatsQuery,
  useGetRecentInventoryQuery,
  useGetLowStockItemsQuery,
  useGetRecentInvoicesQuery,
  useGetCategoryDistributionQuery,
} = dashboardApiSlice;
