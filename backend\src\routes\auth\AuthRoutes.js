const express = require('express');
const router = express.Router();
const validations = require('./AuthValidations');
const AuthController = require('./AuthController');
const { validate } = require('../../util/validations');
const { verifyToken } = require('../../util/auth');

router.post('/msg91-send-otp', AuthController.sendOtp);
router.post('/msg91-validate-otp', AuthController.validateOtp);
router.post('/msg91-resend-otp', AuthController.resendOtp);
router.post('/request-otp', validate(validations.requestOtp), AuthController.requestOtp);
router.post('/verify-otp', validate(validations.verifyOtp), AuthController.verifyOtp);
router.post('/sign-up', validate(validations.signUp), AuthController.signUp);
router.post('/fcm-signup', AuthController.fcmSignUp);
router.post('/log-in', validate(validations.logIn), AuthController.logIn);
router.post('/log-out', verifyToken, AuthController.logout);
router.post('/reset-password', validate(validations.resetPassword), AuthController.resetPassword);

module.exports = router;
