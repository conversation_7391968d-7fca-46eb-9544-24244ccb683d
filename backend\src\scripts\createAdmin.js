require('custom-env').env('api');
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const Admin = require('../../lib/models/models/Admin.model');
const { AdminRole, Permissions } = require('../../lib/models/enums');

// Define default permissions for each role
const rolePermissions = {
    [AdminRole.ADMIN]: Object.values(Permissions), // Admin has all permissions
    [AdminRole.INVENTORY_MANAGER]: [
        Permissions.VIEW_INVENTORY,
        Permissions.CREATE_INVENTORY,
        Permissions.UPDATE_INVENTORY,
        Permissions.DELETE_INVENTORY,
        Permissions.MANAGE_STOCK,
        Permissions.VIEW_REPORTS
    ],
    [AdminRole.INVOICE_MANAGER]: [
        Permissions.VIEW_INVOICES,
        Permissions.CREATE_INVOICE,
        Permissions.UPDATE_INVOICE,
        Permissions.DELETE_INVOICE,
        Permissions.GENERATE_INVOICE,
        Permissions.VIEW_REPORTS
    ],
    [AdminRole.ACCOUNTANT]: [
        Permissions.VIEW_ACCOUNTS,
        Permissions.MANAGE_TRANSACTIONS,
        Permissions.VIEW_REPORTS,
        Permissions.MANAGE_PAYMENTS,
        Permissions.VIEW_INVOICES
    ],
    [AdminRole.EMPLOYEE]: [
        Permissions.VIEW_INVENTORY,
        Permissions.VIEW_INVOICES,
        Permissions.VIEW_REPORTS
    ]
};

async function createAdmin() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        const users = [
            {
                email: '<EMAIL>',
                firstName: 'Admin',
                lastName: 'User',
                role: AdminRole.ADMIN
            },
            {
                email: '<EMAIL>',
                firstName: 'Inventory',
                lastName: 'Manager',
                role: AdminRole.INVENTORY_MANAGER
            },
            {
                email: '<EMAIL>',
                firstName: 'Invoice',
                lastName: 'Manager',
                role: AdminRole.INVOICE_MANAGER
            },
            {
                email: '<EMAIL>',
                firstName: 'Accountant',
                lastName: 'User',
                role: AdminRole.ACCOUNTANT
            },
            {
                email: '<EMAIL>',
                firstName: 'Employee',
                lastName: 'User',
                role: AdminRole.EMPLOYEE
            }
        ];

        for (const userData of users) {
            // Check if user already exists
            const existingUser = await Admin.findOne({ email: userData.email });
            if (existingUser) {
                console.log(`User ${userData.email} already exists`);
                continue;
            }

            // Create new user with role-based permissions
            const admin = new Admin({
                ...userData,
                password: 'Qwerty12#', // Don't pre-hash the password
                permissions: rolePermissions[userData.role],
                countryCode: '+91',
                contactNumber: '',
                isSuspended: false,
                isDeleted: false,
                failedLoginAttempts: 0,
                preventLoginTill: 0
            });

            await admin.save();
            console.log(`User ${userData.email} created successfully with ${admin.permissions.length} permissions`);
        }

        console.log('All users created successfully');
    } catch (error) {
        console.error('Error creating users:', error);
    } finally {
        await mongoose.disconnect();
    }
}

createAdmin(); 
