const Response = {
    success(data = null, message = 'Success') {
        return this.status(200).json({
            success: true,
            message,
            data
        });
    },

    created(data = null, message = 'Created Successfully') {
        return this.status(201).json({
            success: true,
            message,
            data
        });
    },

    warn(data = null, message = 'Warning') {
        return this.status(400).json({
            success: false,
            message,
            data
        });
    },

    unauthorized(data = null, message = 'Unauthorized') {
        return this.status(401).json({
            success: false,
            message,
            data
        });
    },

    forbidden(data = null, message = 'Forbidden') {
        return this.status(403).json({
            success: false,
            message,
            data
        });
    },

    notFound(data = null, message = 'Not Found') {
        return this.status(404).json({
            success: false,
            message,
            data
        });
    },

    error(data = null, message = 'Internal Server Error') {
        return this.status(500).json({
            success: false,
            message,
            data
        });
    }
};

module.exports = {
    Response
}; 