import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import EyeIcon from "@mui/icons-material/Visibility";
import DownloadIcon from "@mui/icons-material/Download";
import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import Header from "components/Header";
import StyledDataGrid from "components/datagrids/StyledDataGrid";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useDeleteCustomerMutation, useGetCustomersQuery } from "services";
import { setConfirmModalConfig } from "store/slices/utilSlice";
import { tokens } from "theme";
import dataGridStyles from "../../styles/dataGridStyles";
import { useDebounce, useDeleteRecord } from "hooks";
import { Search } from "@mui/icons-material";
import {
  useDownloadInvoiceMutation,
  useGenerateInvoiceMutation,
  useGetInvoicesQuery,
} from "services/invoices.service";

const Invoices = () => {
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(100);
  const [searchParams, setSearchParams] = useSearchParams();
  const searchName = searchParams.get("name") || "";
  const debouncedSearch = useDebounce(searchName, 500);
  const [filterVisible, setFilterVisible] = useState(false);
  const dispatch = useDispatch();
  const userInfo = useSelector((state) => state.auth.user);
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const styles = dataGridStyles(theme.palette.mode);
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));

  const {
    data = {},
    isLoading,
    refetch,
  } = useGetInvoicesQuery(
    {
      params: {
        page: page + 1,
        limit: pageSize,
        search: debouncedSearch ?? undefined,
      },
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );
  const { invoices: invoicesData = [], pagination = {} } = data ?? {};
  const [generateInvoice] = useGenerateInvoiceMutation();
  const [downloadInvoice] = useDownloadInvoiceMutation();

  const handleSearchChange = (evt) => {
    setSearchParams({
      name: evt.target.value,
    });
  };

  const handleClearSearch = () => {
    setSearchParams({});
  };

  const toggleFilterVisibility = () => {
    setFilterVisible(!filterVisible);
  };

  const columns = [
    {
      field: "invoiceNumber",
      headerName: "Invoice Number",
      flex: 1,
      cellClassName: "name-column--cell",
      renderCell: (params) => {
        return (
          <span
            style={{
              textTransform: "capitalize",
            }}
          >
            {params.row.invoiceNumber}
          </span>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      cellClassName: "name-column--cell",
      renderCell: (params) => {
        return (
          <span
            style={{
              textTransform: "capitalize",
            }}
          >
            {params.row.status}
          </span>
        );
      },
    },
    {
      field: "total",
      headerName: "Total",
      flex: 1,
      cellClassName: "name-column--cell",
      renderCell: (params) => {
        return (
          <span
            style={{
              textTransform: "capitalize",
            }}
          >
            {params.row.total}
          </span>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
      renderCell: (params) => {
        const onGenerateInvoice = async () => {
          try {
            // if (params.row.status !== "generated") {
              const result = await generateInvoice({ invoiceId: params.row._id });
            // }
            // const result = await downloadInvoice({ invoiceId: params.row._id });
            const url = window.URL.createObjectURL(new Blob([result.data]));
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute(
              "download",
              `invoice-${params.row.invoiceNumber}.pdf`
            );
            document.body.appendChild(link);
            link.click();
          } catch (error) {}
        };
        return (
          <Stack direction="row" spacing={1} justifyContent="center">
            <Tooltip title="View Details">
              <IconButton
                component={Link}
                to={`/invoices/${params.row._id}`}
                size="small"
                sx={{
                  color: colors.blackAccent[700],
                  "&:hover": { color: colors.primary[500] },
                }}
              >
                <EyeIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Generate Invoice">
              <IconButton
                size="small"
                sx={{
                  color: colors.blackAccent[700],
                  "&:hover": { color: colors.primary[500] },
                }}
                onClick={onGenerateInvoice}
              >
                <DownloadIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        );
      },
    },
  ];

  return (
    <Box m="20px">
      <Box
        sx={{
          ...styles.headingsContainer,
          ...styles.dFlex,
          ...styles.alignItemsStart,
          ...styles.justifyContentBetween,
        }}
      >
        <Header title="INVOICES" subtitle="Managing the Invoices" />

        <Box>
          <Button sx={styles.buttonMd} LinkComponent={Link} to="/invoices/add">
            <AddOutlinedIcon sx={{ mr: "10px" }} />
            Add Invoice
          </Button>
        </Box>
      </Box>
      <Box
        sx={{
          ...styles.formContainer,
          p: { xs: 1, sm: 2 },
          borderRadius: "12px",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            alignItems: { xs: "stretch", sm: "center" },
            justifyContent: "space-between",
            gap: 2,
            mb: 2,
          }}
        >
          <TextField
            placeholder="Search by name"
            onChange={handleSearchChange}
            value={searchName}
            fullWidth
            size={isMobile ? "small" : "medium"}
            sx={{
              maxWidth: { sm: "400px" },
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px",
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: searchName && (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear search"
                    onClick={handleClearSearch}
                    edge="end"
                    size="small"
                  >
                    <Clear fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          {/* <Box sx={{ display: { xs: "none", md: "block" } }}>
            <Typography variant="body2" color="text.secondary">
              {pagination.totalItems || 0} items found
            </Typography>
          </Box> */}
        </Box>
        <StyledDataGrid
          rows={invoicesData}
          columns={columns}
          getRowId={(row) => row._id}
          loading={isLoading}
          pagination
          page={page}
          pageSize={pageSize}
          rowCount={pagination.totalItems || 0}
          paginationMode="server"
          onPageChange={(newPage) => setPage(newPage)}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          className="no-shadow"
          noResultText="No inventory items found."
          enableFiltering={true}
          enableColumnVisibilityToggle={true}
          enableDensitySelector={true}
          enableExport={true}
          containerStyle={{
            borderRadius: "8px",
            height: isMobile ? "60vh" : isTablet ? "65vh" : "70vh",
          }}
          customStyle={{
            "& .MuiDataGrid-toolbarContainer": {
              padding: isMobile ? "8px 4px" : "16px 8px",
              flexWrap: "wrap",
              gap: "8px",
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: "name", sort: "asc" }],
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default Invoices;
