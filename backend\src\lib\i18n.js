const languages = {
    en: {
        validation: {
            any: {
                required: '{{#label}} is required',
                empty: '{{#label}} cannot be empty'
            },
            string: {
                regex: {
                    name: '{{#label}} must match the pattern {{#name}}',
                    base: '{{#label}} must be a string'
                },
                max: '{{#label}} must be less than or equal to {{#limit}} characters long',
                min: '{{#label}} must be at least {{#limit}} characters long'
            },
            number: {
                base: '{{#label}} must be a number',
                min: '{{#label}} must be greater than or equal to {{#limit}}',
                max: '{{#label}} must be less than or equal to {{#limit}}'
            },
            objectId: {
                valid: '{{#label}} must be a valid ObjectId'
            }
        }
    }
};

const __ = (key, params = {}) => {
    const language = 'en'; // Default to English
    const keys = key.split('.');
    let value = languages[language];
    
    for (const k of keys) {
        if (!value || !value[k]) {
            return key;
        }
        value = value[k];
    }

    return value.replace(/\{\{#(\w+)\}\}/g, (match, param) => params[param] || match);
};

module.exports = {
    __,
    languages
}; 