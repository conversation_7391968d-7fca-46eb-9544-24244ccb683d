import {
  Box,
  Grid,
  InputLabel,
  Typography,
  useTheme,
  Card,
  CardContent,
  Divider,
  Chip,
  Paper,
  Avatar,
  Stack,
  IconButton,
} from "@mui/material";
import { LuxuryButton, PrintLabel } from "components";
import Header from "components/Header";
import parse from "html-react-parser";
import { useNavigate, useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { useGetInventoriesQuery } from "services";
import dataGridStyles from "../../styles/dataGridStyles";
import { checkGoldItems, checkOtherItems, formatMediaUrl } from "utils/common";
import { tokens } from "../../theme";

// Icons
import DiamondIcon from "@mui/icons-material/Diamond";
import BalanceIcon from "@mui/icons-material/Balance";
import PaidIcon from "@mui/icons-material/Paid";
import InventoryIcon from "@mui/icons-material/Inventory";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import InfoIcon from "@mui/icons-material/Info";
import EditIcon from "@mui/icons-material/Edit";
import LocalPrintshopIcon from "@mui/icons-material/LocalPrintshop";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";

const View = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const styles = dataGridStyles(theme.palette.mode);
  const { id } = useParams();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);



  const { data: inventoryData } = useGetInventoriesQuery(
    { id },
    { skip: !id, refetchOnMountOrArgChange: true }
  );

  const navigate = useNavigate();

  // Image carousel functions
  const handlePrevImage = () => {
    if (inventoryData?.images?.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? inventoryData.images.length - 1 : prev - 1
      );
    }
  };

  const handleNextImage = () => {
    if (inventoryData?.images?.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === inventoryData.images.length - 1 ? 0 : prev + 1
      );
    }
  };

  // Reset image index when inventory data changes
  useEffect(() => {
    setCurrentImageIndex(0);
  }, [inventoryData]);

  // Custom styles for the luxury view
  const viewStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.08)",
      overflow: "hidden",
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      height: "100%",
      "&:hover": {
        transform: "translateY(-5px)",
        boxShadow: "0px 8px 30px rgba(0, 0, 0, 0.12)",
      },
    },
    sectionTitle: {
      fontFamily: '"Playfair Display", serif',
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: "relative",
      display: "inline-block",
      paddingBottom: "8px",
      "&::after": {
        content: '""',
        position: "absolute",
        width: "40%",
        height: "2px",
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
    infoLabel: {
      color: colors.blackAccent[700],
      fontWeight: 500,
      fontSize: "0.9rem",
      mb: 0.5,
    },
    infoValue: {
      color: colors.blackAccent[900],
      fontWeight: 400,
      fontSize: "1rem",
      mb: 2,
    },
    qrCodeContainer: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "column",
      mt: 3,
      mb: 3,
    },
    imageContainer: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      mt: 3,
      mb: 3,
      height: "200px",
      backgroundColor: colors.whiteAccent[200],
      borderRadius: "8px",
      overflow: "hidden",
    },
    sectionIcon: {
      color: colors.primary[500],
      mr: 1,
      fontSize: "1.5rem",
    },
    sectionHeader: {
      display: "flex",
      alignItems: "center",
      mb: 2,
    },
    chip: {
      backgroundColor: colors.primary[100],
      color: colors.primary[700],
      fontWeight: 500,
      m: 0.5,
    },
  };

  // Format values with proper capitalization and handling nulls
  const formatValue = (value) => {
    if (value === null || value === undefined) return "N/A";
    if (typeof value === "string")
      return value.charAt(0).toUpperCase() + value.slice(1);
    return value;
  };



  return (
    <Box m="20px">
      <Box sx={styles.mainHeadings}>
        <Header
          title="INVENTORY DETAILS"
          subtitle={
            inventoryData?.name
              ? `Viewing details for ${inventoryData.name}`
              : "Loading item details..."
          }
        />
        <Box mb={3}></Box>
        <Box display="flex" justifyContent="end" mt="20px" gap={2}>
          {inventoryData && (
            <LuxuryButton
              onClick={() => navigate(`/inventory/edit/${id}`)}
              sx={{ mt: 1 }}
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
            >
              Edit Inventory
            </LuxuryButton>
          )}
          <LuxuryButton
            onClick={() => navigate("/inventory")}
            sx={{ mt: 1 }}
            variant="outlined"
          >
            Back to Inventory
          </LuxuryButton>
        </Box>
      </Box>

      {/* Label Preview Section */}
      {inventoryData && (
        <Card sx={{ ...viewStyles.card, mb: 3 }}>
          <CardContent>
            <Box sx={viewStyles.sectionHeader}>
              <LocalPrintshopIcon sx={viewStyles.sectionIcon} />
              <Typography variant="h4" sx={viewStyles.sectionTitle}>
                Label Preview
              </Typography>
            </Box>
            <Divider sx={viewStyles.divider} />

            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 2 }}>
              <Box
                sx={{
                  width: '60mm',
                  height: '15mm',
                  border: `1px solid ${colors.blackAccent[300]}`,
                  borderRadius: '4px',
                  p: 0.5,
                  mb: 2,
                  backgroundColor: colors.whiteAccent[100],
                  fontFamily: 'Arial, sans-serif',
                  fontSize: '10.5px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', height: '100%' }}>
                  {/* Header row */}
                  <Box sx={{ display: 'flex', mb: 0.5 }}>
                    <Box sx={{ width: '50%', fontSize: '10.5px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {inventoryData.name}
                    </Box>
                    <Box sx={{ width: '50%', fontSize: '10.5px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {inventoryData.sku}{inventoryData.size ? ` (${inventoryData.size})` : ""}
                    </Box>
                  </Box>

                  {/* Content row */}
                  <Box sx={{ display: 'flex' }}>
                    {/* Left column - weights */}
                    <Box sx={{ width: '40%', fontSize: '10.5px', pr: 0.5 }}>
                      {inventoryData.weight?.grossWt?.value &&
                        <Box sx={{ mb: 0.2 }}>GW: {inventoryData.weight.grossWt.value} {inventoryData.weight.grossWt.unit || "ct"}</Box>}

                      {inventoryData.weight?.netWt?.value &&
                        <Box sx={{ mb: 0.2 }}>NW: {inventoryData.weight.netWt.value} {inventoryData.weight.netWt.unit || "ct"}</Box>}

                      {inventoryData.weight?.stoneWt?.value &&
                        <Box sx={{ mb: 0.2 }}>SW: {inventoryData.weight.stoneWt.value} {inventoryData.weight.stoneWt.unit || "ct"}</Box>}
                    </Box>

                    {/* Middle column - prices and other weights */}
                    <Box sx={{ width: '40%', fontSize: '10.5px', pr: 0.5 }}>
                      {inventoryData.price?.costPrice &&
                        <Box sx={{ mb: 0.2 }}>CP: {inventoryData.price.costPrice}</Box>}

                      {inventoryData.price?.sellingPrice &&
                        <Box sx={{ mb: 0.2 }}>SP: ${inventoryData.price.sellingPrice}</Box>}

                      {inventoryData.weight?.diamondWt?.value &&
                        <Box sx={{ mb: 0.2 }}>DW: {inventoryData.weight.diamondWt.value} {inventoryData.weight.diamondWt.unit || "ct"}</Box>}

                      {inventoryData.weight?.averageWt?.value &&
                        <Box sx={{ mb: 0.2 }}>AVG: {inventoryData.weight.averageWt.value} {inventoryData.weight.averageWt.unit || "ct"}</Box>}
                    </Box>

                    {/* Right column - QR code */}
                    <Box sx={{ width: '20%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                      {inventoryData.qrCode && (
                        <Box sx={{ width: '9mm', height: '9mm', border: '0.5px solid #ddd', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <img
                            src={formatMediaUrl(inventoryData.qrCode)}
                            alt="QR"
                            style={{ width: '8mm', height: '8mm', objectFit: 'contain', display: 'block' }}
                          />
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: 'center' }}>
                This is how your label will appear when printed. Click the Print Label button to print.
              </Typography>

              <PrintLabel
                item={inventoryData}
                buttonSize="medium"
                buttonSx={{ mt: 1 }}
              />
            </Box>
          </CardContent>
        </Card>
      )}

      <Grid container spacing={3}>
        {/* LEFT COLUMN - Basic Info and QR Code */}
        <Grid item xs={12} md={4}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <DiamondIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Basic Information
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />

              {/* Item Image Carousel */}
              <Box sx={{
                ...viewStyles.imageContainer,
                position: 'relative',
                overflow: 'hidden'
              }}>
                {inventoryData?.images?.length > 0 ? (
                  <>
                    {/* Main Image */}
                    <img
                      src={formatMediaUrl(inventoryData.images[currentImageIndex])}
                      alt={`${inventoryData?.name || "Jewelry item"} - Image ${currentImageIndex + 1}`}
                      style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "contain",
                        transition: "opacity 0.3s ease",
                      }}
                    />

                    {/* Navigation Arrows - Only show if more than 1 image */}
                    {inventoryData.images.length > 1 && (
                      <>
                        <IconButton
                          onClick={handlePrevImage}
                          sx={{
                            position: 'absolute',
                            left: 8,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            color: 'white',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            },
                            width: 40,
                            height: 40,
                          }}
                        >
                          <ArrowBackIosIcon fontSize="small" />
                        </IconButton>

                        <IconButton
                          onClick={handleNextImage}
                          sx={{
                            position: 'absolute',
                            right: 8,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            color: 'white',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            },
                            width: 40,
                            height: 40,
                          }}
                        >
                          <ArrowForwardIosIcon fontSize="small" />
                        </IconButton>
                      </>
                    )}

                    {/* Image Counter */}
                    {inventoryData.images.length > 1 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 8,
                          right: 8,
                          backgroundColor: 'rgba(0, 0, 0, 0.7)',
                          color: 'white',
                          px: 1,
                          py: 0.5,
                          borderRadius: '12px',
                          fontSize: '0.75rem',
                          fontWeight: 500,
                        }}
                      >
                        {currentImageIndex + 1} / {inventoryData.images.length}
                      </Box>
                    )}

                    {/* Dot Indicators */}
                    {inventoryData.images.length > 1 && inventoryData.images.length <= 5 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 8,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          display: 'flex',
                          gap: 1,
                        }}
                      >
                        {inventoryData.images.map((_, index) => (
                          <Box
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: index === currentImageIndex
                                ? colors.primary[500]
                                : 'rgba(255, 255, 255, 0.5)',
                              cursor: 'pointer',
                              transition: 'all 0.3s ease',
                              '&:hover': {
                                backgroundColor: index === currentImageIndex
                                  ? colors.primary[600]
                                  : 'rgba(255, 255, 255, 0.8)',
                              },
                            }}
                          />
                        ))}
                      </Box>
                    )}
                  </>
                ) : (
                  <DiamondIcon
                    sx={{ fontSize: 80, color: colors.primary[300] }}
                  />
                )}
              </Box>

              {/* Image Navigation Info */}
              {inventoryData?.images?.length > 1 && (
                <Box sx={{ mt: 2, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {inventoryData.images.length} images available - Use arrows or dots to navigate
                  </Typography>
                </Box>
              )}

              {/* Basic Info Fields */}
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography
                    variant="h5"
                    sx={{ fontWeight: 600, color: colors.primary[500], mb: 1 }}
                  >
                    {formatValue(inventoryData?.name)}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>SKU</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatValue(inventoryData?.sku)}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>Category</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatValue(inventoryData?.category)}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>Sizes</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {inventoryData?.sizes && inventoryData.sizes.length > 0 ? (
                      inventoryData.sizes.map((size, index) => (
                        <Chip
                          key={index}
                          label={`${size.value} (${size.quantity})`}
                          size="small"
                          sx={{
                            backgroundColor: colors.primary[100],
                            color: colors.primary[700],
                            mb: 0.5
                          }}
                        />
                      ))
                    ) : (
                      <Typography sx={viewStyles.infoValue}>
                        {formatValue(inventoryData?.size) || "N/A"}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {checkGoldItems(inventoryData?.category) && (
                  <Grid item xs={6}>
                    <Typography sx={viewStyles.infoLabel}>Purity</Typography>
                    <Typography sx={viewStyles.infoValue}>
                      {formatValue(inventoryData?.purity)}
                    </Typography>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Typography sx={viewStyles.infoLabel}>Status</Typography>
                  <Chip
                    label={formatValue(inventoryData?.status)}
                    sx={{
                      ...viewStyles.chip,
                      backgroundColor:
                        inventoryData?.status === "available"
                          ? colors.greenAccent[100]
                          : colors.primary[100],
                      color:
                        inventoryData?.status === "available"
                          ? colors.greenAccent[700]
                          : colors.primary[700],
                    }}
                  />
                </Grid>
              </Grid>

              {/* QR Code Section */}
              {inventoryData?.qrCode && (
                <Box sx={viewStyles.qrCodeContainer}>
                  <Typography
                    variant="h6"
                    sx={{ mb: 1, color: colors.blackAccent[700] }}
                  >
                    QR Code
                  </Typography>
                  <Paper
                    elevation={3}
                    sx={{
                      p: 2,
                      borderRadius: "12px",
                      overflow: "hidden",
                      backgroundColor: colors.whiteAccent[100],
                    }}
                  >
                    <img
                      src={formatMediaUrl(inventoryData?.qrCode)}
                      alt="QR Code"
                      width={150}
                      height={150}
                      style={{ display: "block" }}
                    />
                  </Paper>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* RIGHT COLUMN - Detailed Information */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* Weight & Measurements Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <BalanceIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Weight & Measurements
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>
                        Gross Weight
                      </Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.weight?.grossWt?.value
                          ? `${inventoryData.weight.grossWt.value} ${inventoryData.weight.grossWt.unit || "ct"
                          }`
                          : "N/A"}
                      </Typography>
                    </Grid>
                    {checkGoldItems(inventoryData?.category) && (
                      <>
                        <Grid item xs={6}>
                          <Typography sx={viewStyles.infoLabel}>
                            Net Weight
                          </Typography>
                          <Typography sx={viewStyles.infoValue}>
                            {inventoryData?.weight?.netWt?.value
                              ? `${inventoryData.weight.netWt.value} ${inventoryData.weight.netWt.unit || "ct"
                              }`
                              : "N/A"}
                          </Typography>
                        </Grid>

                        <Grid item xs={6}>
                          <Typography sx={viewStyles.infoLabel}>
                            Stone Weight
                          </Typography>
                          <Typography sx={viewStyles.infoValue}>
                            {inventoryData?.weight?.stoneWt?.value
                              ? `${inventoryData.weight.stoneWt.value} ${inventoryData.weight.stoneWt.unit || "ct"
                              }`
                              : "N/A"}
                          </Typography>
                        </Grid>

                        <Grid item xs={6}>
                          <Typography sx={viewStyles.infoLabel}>
                            Diamond Weight
                          </Typography>
                          <Typography sx={viewStyles.infoValue}>
                            {inventoryData?.weight?.diamondWt?.value
                              ? `${inventoryData.weight.diamondWt.value} ${inventoryData.weight.diamondWt.unit || "ct"
                              }`
                              : "N/A"}
                          </Typography>
                        </Grid>
                      </>
                    )}
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>
                        Average Weight
                      </Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.weight?.averageWt?.value
                          ? `${inventoryData.weight.averageWt.value} ${inventoryData.weight.averageWt.unit || "ct"
                          }`
                          : "N/A"}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Pricing Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <PaidIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Pricing Details
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>
                        Cost Price
                      </Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.price?.costPrice
                          ? `$${inventoryData.price.costPrice}`
                          : "N/A"}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>
                        Selling Price
                      </Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.price?.sellingPrice
                          ? `$${inventoryData.price.sellingPrice.toLocaleString()}`
                          : "N/A"}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>MRP</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.price?.mrp
                          ? `$${inventoryData.price.mrp.toLocaleString()}`
                          : "N/A"}
                      </Typography>
                    </Grid>

                    {checkOtherItems(inventoryData?.category) && (
                      <Grid item xs={6}>
                        <Typography sx={viewStyles.infoLabel}>
                          Making Charges
                        </Typography>
                        <Typography sx={viewStyles.infoValue}>
                          {inventoryData?.price?.makingCharges
                            ? `$${inventoryData.price.makingCharges.toLocaleString()}`
                            : "N/A"}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Inventory Management Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <InventoryIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Inventory Management
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>
                        Quantity
                      </Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.stock?.quantity !== undefined
                          ? inventoryData.stock.quantity
                          : "N/A"}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>
                        Min Stock Level
                      </Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.stock?.minStockLevel !== undefined
                          ? inventoryData.stock.minStockLevel
                          : "N/A"}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Typography sx={viewStyles.infoLabel}>
                        Stock Status
                      </Typography>
                      <Chip
                        label={
                          inventoryData?.isLowStock ? "Low Stock" : "In Stock"
                        }
                        sx={{
                          ...viewStyles.chip,
                          backgroundColor: inventoryData?.isLowStock
                            ? colors.redAccent[100]
                            : colors.greenAccent[100],
                          color: inventoryData?.isLowStock
                            ? colors.redAccent[700]
                            : colors.greenAccent[700],
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Tags & Additional Info Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <LocalOfferIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Tags & Additional Info
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  {/* Tags */}
                  <Typography sx={viewStyles.infoLabel}>Tags</Typography>
                  <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
                    {inventoryData?.tags?.length > 0 ? (
                      inventoryData.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={formatValue(tag)}
                          sx={viewStyles.chip}
                        />
                      ))
                    ) : (
                      <Typography
                        sx={{
                          color: colors.blackAccent[500],
                          fontStyle: "italic",
                        }}
                      >
                        No tags
                      </Typography>
                    )}
                  </Box>

                  {/* GST Info */}
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Has GST</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {inventoryData?.hasGST ? "Yes" : "No"}
                      </Typography>
                    </Grid>

                    {inventoryData?.hasGST && (
                      <Grid item xs={6}>
                        <Typography sx={viewStyles.infoLabel}>
                          GST Rate
                        </Typography>
                        <Typography sx={viewStyles.infoValue}>
                          {inventoryData?.gstRate
                            ? `${inventoryData.gstRate}%`
                            : "N/A"}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Description Section */}
            {inventoryData?.description && (
              <Grid item xs={12}>
                <Card sx={viewStyles.card}>
                  <CardContent>
                    <Box sx={viewStyles.sectionHeader}>
                      <InfoIcon sx={viewStyles.sectionIcon} />
                      <Typography variant="h4" sx={viewStyles.sectionTitle}>
                        Description
                      </Typography>
                    </Box>
                    <Divider sx={viewStyles.divider} />

                    <Box
                      sx={{
                        p: 2,
                        backgroundColor: colors.whiteAccent[200],
                        borderRadius: "8px",
                      }}
                    >
                      {parse(inventoryData?.description ?? "")}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

// We no longer need the InfoField helper component as we've created a more sophisticated layout

export default View;
