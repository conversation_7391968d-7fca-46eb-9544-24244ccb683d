const mongoose = require('mongoose');

const ContactUsSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
    },
    type: {
        type: String,
        default: '',
    },
    message: {
        type: String,
        default: '',
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

// Check if the model has already been compiled
const ContactUs = mongoose.models.ContactUs || mongoose.model('ContactUs', ContactUsSchema);

module.exports = ContactUs;
