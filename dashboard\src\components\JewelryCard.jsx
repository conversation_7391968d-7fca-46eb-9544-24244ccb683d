import React from 'react';
import {
  Card,
  CardContent,
  CardMedia,
  Typography,
  Box,
  Chip,
  Divider,
  IconButton,
  useTheme
} from '@mui/material';
import { tokens } from '../theme';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

const JewelryCard = ({
  item,
  onView,
  onEdit,
  onDelete,
  showActions = true
}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  return (
    <Card
      sx={{
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
        }
      }}
    >
      <CardMedia
        component="img"
        height={{ xs: "180", sm: "200" }}
        image={item.images?.[0] || 'https://via.placeholder.com/300x200?text=Jewelry+Item'}
        alt={item.name}
        sx={{ objectFit: 'cover', height: '150px' }}
      />

      <CardContent sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography
            variant="h4"
            component="div"
            sx={{
              fontFamily: '"Playfair Display", serif',
              color: colors.primary[500],
              fontWeight: 500,
              mb: 0.5,
              fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' }
            }}
          >
            {item.name}
          </Typography>

          <Chip
            label={item.category}
            size="small"
            sx={{
              backgroundColor: colors.primary[100],
              color: colors.primary[700],
              fontWeight: 500,
              borderRadius: '4px',
              fontSize: { xs: '0.7rem', sm: '0.75rem' }
            }}
          />
        </Box>

        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 2,
            color: colors.blackAccent[600],
            height: '40px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}
        >
          {item.description || 'No description available'}
        </Typography>

        <Divider sx={{
          my: 2,
          background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
          opacity: 0.5
        }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" sx={{ color: colors.blackAccent[700], fontWeight: 500 }}>
            SKU:
          </Typography>
          <Typography variant="body2" sx={{ color: colors.blackAccent[600] }}>
            {item.sku}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" sx={{ color: colors.blackAccent[700], fontWeight: 500 }}>
            Weight:
          </Typography>
          <Typography variant="body2" sx={{ color: colors.blackAccent[600] }}>
            {item.weight?.netWt?.value || 0} {item.weight?.netWt?.unit || 'g'}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" sx={{ color: colors.blackAccent[700], fontWeight: 500 }}>
            Purity:
          </Typography>
          <Typography variant="body2" sx={{ color: colors.blackAccent[600] }}>
            {item.purity || 'N/A'}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" sx={{ color: colors.blackAccent[700], fontWeight: 500 }}>
            Stock:
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: item.stock?.quantity > 0 ? colors.greenAccent[600] : 'error.main',
              fontWeight: 500
            }}
          >
            {item.stock?.quantity || 0} units
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: colors.primary[500],
              fontFamily: '"Playfair Display", serif',
              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' }
            }}
          >
            ₹{item.price?.sellingPrice?.toLocaleString() || 0}
          </Typography>

          {showActions && (
            <Box>
              {onView && (
                <IconButton
                  onClick={() => onView(item._id)}
                  size="small"
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.primary[500] }
                  }}
                >
                  <VisibilityOutlinedIcon />
                </IconButton>
              )}

              {onEdit && (
                <IconButton
                  onClick={() => onEdit(item._id)}
                  size="small"
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.primary[500] }
                  }}
                >
                  <EditOutlinedIcon />
                </IconButton>
              )}

              {onDelete && (
                <IconButton
                  onClick={() => onDelete(item._id)}
                  size="small"
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: 'error.main' }
                  }}
                >
                  <DeleteOutlineOutlinedIcon />
                </IconButton>
              )}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default JewelryCard;
