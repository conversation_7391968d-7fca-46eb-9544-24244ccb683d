const path = require('path');
const fs = require('fs');
const glob = require('glob');
const swaggerJsdoc = require('swagger-jsdoc');

class SwaggerGenerator {
    constructor() {
        this.baseDir = path.join(__dirname, '../routes');
        this.swaggerDefinition = {
            openapi: '3.0.2',
            info: {
                title: process.env.SITE_TITLE || 'API Documentation',
                version: '1.0.0',
            },
            servers: [
                {
                    url: process.env.API_URL || 'http://localhost:2004/api',
                }
            ],
            components: {
                securitySchemes: {
                    ApiKeyAuth: {
                        type: 'apiKey',
                        in: 'header',
                        name: 'Authorization'
                    }
                },
                parameters: {
                    headerLanguage: {
                        name: 'Accept-Language',
                        in: 'header',
                        required: true,
                        schema: {
                            type: 'string',
                            default: 'en',
                            enum: ['en', 'ru', 'kk']
                        }
                    },
                    headerPlatform: {
                        name: 'X-ESHOP-Platform',
                        in: 'header',
                        required: true,
                        schema: {
                            type: 'string',
                            default: 'ios',
                            enum: ['ios', 'android']
                        }
                    },
                    headerVersion: {
                        name: 'X-ESHOP-Version',
                        in: 'header',
                        required: true,
                        schema: {
                            type: 'string',
                            default: '1.0.0',
                            pattern: '^[\\d]+\\.[\\d]+\\.[\\d]+$'
                        }
                    }
                },
                schemas: {
                    ApiResponse: {
                        type: 'object',
                        required: ['success', 'message', 'meta'],
                        properties: {
                            success: {
                                type: 'boolean'
                            },
                            message: {
                                type: 'string'
                            },
                            data: {},
                            meta: {
                                type: 'object',
                                properties: {
                                    version: {
                                        type: 'string'
                                    },
                                    forceUpdate: {
                                        type: 'boolean'
                                    },
                                    maintenance: {
                                        type: 'boolean'
                                    },
                                    hasUpdate: {
                                        type: 'boolean'
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };
    }

    generateTags() {
        const dirs = fs.readdirSync(this.baseDir)
            .filter(file => fs.statSync(path.join(this.baseDir, file)).isDirectory());
        
        return dirs.map(dir => ({
            name: dir.charAt(0).toUpperCase() + dir.slice(1),
            description: `Operations related to ${dir}`
        }));
    }

    findControllerFiles() {
        const files = glob.sync('**/*Controller.js', { cwd: this.baseDir });
        return files;
    }

    parseControllerComments(filePath) {
        const fullPath = path.join(this.baseDir, filePath);
        const content = fs.readFileSync(fullPath, 'utf8');
        const routeGroup = path.dirname(filePath);
        
        // Extract JSDoc comments and method information
        const methodRegex = /async\s+(\w+)\s*\([^)]*\)\s*{/g;
        const methods = [];
        let match;

        while ((match = methodRegex.exec(content)) !== null) {
            const methodName = match[1];
            methods.push(this.generateMethodDoc(routeGroup, methodName));
        }

        return methods;
    }

    generateMethodDoc(routeGroup, methodName) {
        const routePath = `/${routeGroup}`;
        const lowercaseMethod = methodName.toLowerCase();
        let httpMethod = 'get';

        if (lowercaseMethod.includes('create') || lowercaseMethod.includes('add')) {
            httpMethod = 'post';
        } else if (lowercaseMethod.includes('update') || lowercaseMethod.includes('edit')) {
            httpMethod = 'put';
        } else if (lowercaseMethod.includes('delete') || lowercaseMethod.includes('remove')) {
            httpMethod = 'delete';
        }

        return {
            [routePath]: {
                [httpMethod]: {
                    tags: [routeGroup.charAt(0).toUpperCase() + routeGroup.slice(1)],
                    summary: `${methodName} operation`,
                    operationId: methodName,
                    parameters: [
                        { $ref: '#/components/parameters/headerLanguage' },
                        { $ref: '#/components/parameters/headerPlatform' },
                        { $ref: '#/components/parameters/headerVersion' }
                    ],
                    responses: {
                        '200': {
                            description: 'Successful operation',
                            content: {
                                'application/json': {
                                    schema: {
                                        $ref: '#/components/schemas/ApiResponse'
                                    }
                                }
                            }
                        },
                        '400': {
                            description: 'Bad Request'
                        }
                    }
                }
            }
        };
    }

    generate() {
        const tags = this.generateTags();
        const paths = {};

        this.findControllerFiles().forEach(file => {
            const methodDocs = this.parseControllerComments(file);
            methodDocs.forEach(doc => {
                Object.assign(paths, doc);
            });
        });

        const options = {
            definition: {
                ...this.swaggerDefinition,
                tags,
                paths
            },
            apis: [], // We're generating everything programmatically
        };

        return swaggerJsdoc(options);
    }
}

module.exports = new SwaggerGenerator();