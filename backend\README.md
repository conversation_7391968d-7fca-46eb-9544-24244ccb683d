# BMS Server

## Getting Started

### Prerequisites
- Node.js v18
- npm (comes with Node.js)
s
### Installing dependencies
1. Run `npm install` in project root to install all required dependencies

### Project Structure
```
backend/
├── lib/            # Shared libraries and utilities
├── src/
│   ├── api/        # API server code
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── routes/
│   │   ├── util/
│   │   └── index.js
│   └── docs/       # API documentation
└── tests/          # Test files
```

### Running servers
- Admin Server: `npm run start:admin` in project root
- API Server: `npm run start:api` in project root

### API Documentation
- Swagger documentation is available at `/api/docs` when running in development mode
- Swagger definition file is located at `src/docs/swagger.yaml`

### Adding dependencies
To add dependencies to a package:
```bash
npm install <dependency-name>
```

For development dependencies:
```bash
npm install --save-dev <dependency-name>
```

### Creating new packages
The preferred location for creating new packages is `lib` folder, and for servers is `src` folder.

```bash
cd lib 
mkdir package-name && cd package-name
npm init
```

### Environment Variables
Create a `.env` file in the root directory with the following variables:
```
PORT=2004
NODE_ENV=development
API_URL=http://localhost:2004/api
SITE_TITLE=BMS API
```

### Available Scripts
- `npm run start` - Start the API server
- `npm run deploy` - Deploy to Firebase
- `npm run test` - Run tests
