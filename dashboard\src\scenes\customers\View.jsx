import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Divider,
  Chip,
  useTheme,
  Stack,
  Avatar,
} from "@mui/material";
import { LuxuryButton } from "components";
import Header from "components/Header";
import { useNavigate, useParams } from "react-router-dom";
import { useGetCustomerInfoQuery } from "services";
import { tokens } from "theme";
// Icons
import PersonIcon from "@mui/icons-material/Person";
import ContactPhoneIcon from "@mui/icons-material/ContactPhone";
import HomeIcon from "@mui/icons-material/Home";
import BusinessIcon from "@mui/icons-material/Business";
import CakeIcon from "@mui/icons-material/Cake";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";

const View = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { id } = useParams();
  const navigate = useNavigate();

  const { data: customerData } = useGetCustomerInfoQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
  });

  const viewStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.08)",
      overflow: "hidden",
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      height: "100%",
      "&:hover": {
        transform: "translateY(-5px)",
        boxShadow: "0px 8px 30px rgba(0, 0, 0, 0.12)",
      },
    },
    sectionTitle: {
      fontFamily: '"Playfair Display", serif',
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: "relative",
      display: "inline-block",
      paddingBottom: "8px",
      "&::after": {
        content: '""',
        position: "absolute",
        width: "40%",
        height: "2px",
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
    infoLabel: {
      color: colors.blackAccent[700],
      fontWeight: 500,
      fontSize: "0.9rem",
      mb: 0.5,
    },
    infoValue: {
      color: colors.blackAccent[900],
      fontWeight: 400,
      fontSize: "1rem",
      mb: 2,
    },
    chip: {
      backgroundColor: colors.primary[100],
      color: colors.primary[700],
      fontWeight: 500,
      m: 0.5,
    },
    sectionIcon: {
      color: colors.primary[500],
      mr: 1,
      fontSize: "1.5rem",
    },
    sectionHeader: {
      display: "flex",
      alignItems: "center",
      mb: 2,
    },
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return "N/A";
    const d = new Date(dateStr);
    return d.toLocaleDateString();
  };

  const formatValue = (value) => {
    if (value === null || value === undefined) return "N/A";
    if (typeof value === "string")
      return value.charAt(0).toUpperCase() + value.slice(1);
    return value;
  };

  return (
    <Box m="20px">
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Header title="Customer Details" subtitle="View customer information" />
        <LuxuryButton onClick={() => navigate("/customers")}>
          Back to Customers
        </LuxuryButton>
      </Box>

      <Grid container spacing={3}>
        {/* Customer Overview Card */}
        <Grid item xs={12}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    bgcolor: colors.primary[500],
                    mr: 2,
                  }}
                >
                  {customerData?.name?.charAt(0) || "C"}
                </Avatar>
                <Box>
                  <Typography variant="h3" sx={{ color: colors.primary[500] }}>
                    {customerData?.name || "N/A"}
                  </Typography>
                  <Typography variant="subtitle1" sx={{ color: colors.blackAccent[500] }}>
                    Customer ID: {customerData?._id || "N/A"}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Basic Information */}
        <Grid item xs={12} md={6}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <PersonIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Basic Information
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />
              <Stack spacing={2}>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Email</Typography>
                  <Typography sx={viewStyles.infoValue}>{customerData?.email || "N/A"}</Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Customer Type</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatValue(customerData?.customerType)}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Status</Typography>
                  <Chip
                    label={customerData?.isActive ? "Active" : "Inactive"}
                    sx={{
                      backgroundColor: customerData?.isActive
                        ? colors.greenAccent[100]
                        : colors.redAccent[100],
                      color: customerData?.isActive
                        ? colors.greenAccent[700]
                        : colors.redAccent[700],
                    }}
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Contact Information */}
        <Grid item xs={12} md={6}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <ContactPhoneIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Contact Information
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />
              <Stack spacing={2}>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Phone</Typography>
                  <Typography sx={viewStyles.infoValue}>{customerData?.phone || "N/A"}</Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Alternate Phone</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.alternatePhone || "N/A"}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Address Information */}
        <Grid item xs={12} md={6}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <HomeIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Address
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />
              <Stack spacing={2}>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Street</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.address?.street || "N/A"}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>City</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.address?.city || "N/A"}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>State</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.address?.state || "N/A"}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Postal Code</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.address?.postalCode || "N/A"}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Country</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.address?.country || "N/A"}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Business Information */}
        <Grid item xs={12} md={6}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <BusinessIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Business Information
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />
              <Stack spacing={2}>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>GST Number</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.gstNumber || "N/A"}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>PAN Card Number</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.panCardNumber || "N/A"}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Aadhar Number</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {customerData?.aadharNumber || "N/A"}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Personal Information */}
        <Grid item xs={12} md={6}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <CakeIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Personal Information
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />
              <Stack spacing={2}>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Date of Birth</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatDate(customerData?.dateOfBirth)}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Anniversary</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatDate(customerData?.anniversary)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Tags & Meta Info */}
        <Grid item xs={12} md={6}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <LocalOfferIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Tags & Meta Info
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />
              <Stack spacing={2}>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Tags</Typography>
                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                    {customerData?.tags?.length > 0 ? (
                      customerData.tags.map((tag, idx) => (
                        <Chip key={idx} label={tag} sx={viewStyles.chip} />
                      ))
                    ) : (
                      <Typography sx={{ color: colors.blackAccent[500], fontStyle: "italic" }}>
                        No tags
                      </Typography>
                    )}
                  </Box>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Created At</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatDate(customerData?.createdAt)}
                  </Typography>
                </Box>
                <Box>
                  <Typography sx={viewStyles.infoLabel}>Updated At</Typography>
                  <Typography sx={viewStyles.infoValue}>
                    {formatDate(customerData?.updatedAt)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default View;
