const {
    models: { Inventory },
} = require('../../../lib/models');
const mongoose = require('mongoose');

class InventoryController {
    async addInventory(req, res) {
        try {
            // Check if there's a deleted inventory with the same SKU
            const existingDeletedInventory = await Inventory.findOne({
                sku: req.body.sku,
                isDeleted: true
            });

            if (existingDeletedInventory) {
                // If a deleted inventory with the same SKU exists, update it instead of creating a new one
                const updatedInventory = await Inventory.findByIdAndUpdate(
                    existingDeletedInventory._id,
                    {
                        ...req.body,
                        isDeleted: false, // Mark as not deleted
                        createdBy: req.user._id,
                        updatedBy: req.user._id,
                        created: new Date() // Update the creation date to now
                    },
                    { new: true }
                );
                return res.success(updatedInventory, req.__('INVENTORY_ADDED_SUCCESSFULLY'));
            }

            // Check if there's an active inventory with the same SKU
            const existingActiveInventory = await Inventory.findOne({
                sku: req.body.sku,
                isDeleted: false
            });

            if (existingActiveInventory) {
                return res.warn('', req.__('DUPLICATE_SKU'));
            }

            // Create a new inventory item
            const newData = new Inventory({
                ...req.body,
                createdBy: req.user._id
            });

            const saveData = await newData.save();
            return res.success(saveData, req.__('INVENTORY_ADDED_SUCCESSFULLY'));
        } catch (error) {
            if (error.code === 11000) {
                return res.warn('', req.__('DUPLICATE_SKU'));
            }
            console.error('Error adding inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async bulkAddInventory(req, res) {
        const session = await mongoose.startSession();
        try {
            session.startTransaction();

            const items = req.body.items;
            const savedItems = [];

            // Process each item individually to handle deleted items with the same SKU
            for (const item of items) {
                // Check if there's a deleted inventory with the same SKU
                const existingDeletedInventory = await Inventory.findOne({
                    sku: item.sku,
                    isDeleted: true
                }).session(session);

                if (existingDeletedInventory) {
                    // If a deleted inventory with the same SKU exists, update it
                    const updatedInventory = await Inventory.findByIdAndUpdate(
                        existingDeletedInventory._id,
                        {
                            ...item,
                            isDeleted: false,
                            createdBy: req.user._id,
                            updatedBy: req.user._id,
                            created: new Date()
                        },
                        { new: true, session }
                    );
                    savedItems.push(updatedInventory);
                } else {
                    // Check if there's an active inventory with the same SKU
                    const existingActiveInventory = await Inventory.findOne({
                        sku: item.sku,
                        isDeleted: false
                    }).session(session);

                    if (existingActiveInventory) {
                        // Skip this item and continue with the next one
                        continue;
                    }

                    // Create a new inventory item
                    const newItem = new Inventory({
                        ...item,
                        createdBy: req.user._id
                    });
                    const savedItem = await newItem.save({ session });
                    savedItems.push(savedItem);
                }
            }

            await session.commitTransaction();
            return res.success(savedItems, req.__('INVENTORY_BULK_ADDED_SUCCESSFULLY'));
        } catch (error) {
            await session.abortTransaction();
            if (error.code === 11000) {
                return res.warn('', req.__('DUPLICATE_SKU_IN_BULK'));
            }
            console.error('Error bulk adding inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        } finally {
            session.endSession();
        }
    }

    async editInventory(req, res) {
        const { id } = req.params;
        const updateData = req.body;

        try {
            const inventory = await Inventory.findOneAndUpdate(
                { _id: id, isDeleted: false },
                {
                    ...updateData,
                    updatedBy: req.user._id
                },
                { new: true, runValidators: true }
            );

            if (!inventory) {
                return res.warn('', req.__('INVENTORY_NOT_FOUND'));
            }

            return res.success(inventory, req.__('INVENTORY_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            if (error.code === 11000) {
                return res.warn('', req.__('DUPLICATE_SKU'));
            }
            console.error('Error updating inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getInventoryById(req, res) {
        try {
            const inventory = await Inventory.findOne({
                _id: req.params.id,
                isDeleted: false
            }).populate('createdBy', 'firstName lastName')
              .populate('updatedBy', 'firstName lastName');

            if (!inventory) {
                return res.warn('', req.__('INVENTORY_NOT_FOUND'));
            }

            return res.success(inventory);
        } catch (error) {
            console.error('Error retrieving inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async Inventory(req, res) {
        const page = await Inventory.findOne({
            slug: req.params.slug,
            isSuspended: false,
            isDeleted: false,
        });

        if (!page) {
            return res.warn('', req.__('PAGE_NOT_EXISTS'));
        }

        return res.success(page);
    }

    async listInventorys(req, res) {
        try {
            const {
                page = 1,
                limit = 10,
                search,
                category,
                status,
                minStock,
                maxStock,
                minPrice,
                maxPrice,
                sortBy = 'created',
                sortOrder = 'desc'
            } = req.query;

            let query = { isDeleted: false };

            if (search) {
                const searchRegex = new RegExp(search, 'i');
                query.$or = [
                    { name: searchRegex },
                    { sku: searchRegex },
                    { description: searchRegex }
                ];
            }

            if (category) {
                query.category = category;
            }

            if (status) {
                query.status = status;
            }

            if (minStock !== undefined || maxStock !== undefined) {
                query['stock.quantity'] = {};
                if (minStock !== undefined) query['stock.quantity'].$gte = parseInt(minStock);
                if (maxStock !== undefined) query['stock.quantity'].$lte = parseInt(maxStock);
            }

            if (minPrice !== undefined || maxPrice !== undefined) {
                query['price.sellingPrice'] = {};
                if (minPrice !== undefined) query['price.sellingPrice'].$gte = parseFloat(minPrice);
                if (maxPrice !== undefined) query['price.sellingPrice'].$lte = parseFloat(maxPrice);
            }

            const skip = (parseInt(page) - 1) * parseInt(limit);
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

            const [items, totalItems] = await Promise.all([
                Inventory.find(query)
                    .populate('createdBy', 'firstName lastName')
                    .sort(sortOptions)
                    .skip(skip)
                    .limit(parseInt(limit)),
                Inventory.countDocuments(query)
            ]);

            const totalPages = Math.ceil(totalItems / parseInt(limit));
            const hasNextPage = page < totalPages;
            const hasPrevPage = page > 1;

            return res.success({
                items,
                pagination: {
                    totalItems,
                    itemsPerPage: parseInt(limit),
                    totalPages,
                    currentPage: parseInt(page),
                    hasNextPage,
                    hasPrevPage
                }
            });
        } catch (error) {
            console.error('Error listing inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteInventory(req, res) {
        try {
            const inventory = await Inventory.findOneAndUpdate(
                { _id: req.params.id, isDeleted: false },
                {
                    isDeleted: true,
                    updatedBy: req.user._id
                },
                { new: true }
            );

            if (!inventory) {
                return res.warn('', req.__('INVENTORY_NOT_FOUND'));
            }

            return res.success(null, req.__('INVENTORY_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async bulkDeleteInventory(req, res) {
        const session = await mongoose.startSession();
        try {
            session.startTransaction();

            const { ids } = req.body;
            const result = await Inventory.updateMany(
                {
                    _id: { $in: ids },
                    isDeleted: false
                },
                {
                    isDeleted: true,
                    updatedBy: req.user._id
                },
                { session }
            );

            await session.commitTransaction();
            return res.success({ deletedCount: result.nModified }, req.__('INVENTORY_BULK_DELETED_SUCCESSFULLY'));
        } catch (error) {
            await session.abortTransaction();
            console.error('Error bulk deleting inventory:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        } finally {
            session.endSession();
        }
    }

    async updateStock(req, res) {
        try {
            const { id } = req.params;
            const { quantity, operation = 'add' } = req.body;

            const updateQuery = operation === 'add'
                ? { $inc: { 'stock.quantity': quantity } }
                : { $set: { 'stock.quantity': quantity } };

            const inventory = await Inventory.findOneAndUpdate(
                { _id: id, isDeleted: false },
                {
                    ...updateQuery,
                    updatedBy: req.user._id
                },
                { new: true }
            );

            if (!inventory) {
                return res.warn('', req.__('INVENTORY_NOT_FOUND'));
            }

            return res.success(inventory, req.__('STOCK_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating stock:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getLowStockItems(req, res) {
        try {
            const lowStockItems = await Inventory.find({
                isDeleted: false,
                $expr: {
                    $lte: ['$stock.quantity', '$stock.minStockLevel']
                }
            }).populate('createdBy', 'firstName lastName');

            return res.success(lowStockItems);
        } catch (error) {
            console.error('Error getting low stock items:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // New method for inventory stats
    async getInventoryStats(req, res) {
        try {
            // Get total inventory count
            const totalCount = await Inventory.countDocuments({ isDeleted: false });

            // Get low stock count
            const lowStockCount = await Inventory.countDocuments({
                isDeleted: false,
                $expr: { $lte: ['$stock.quantity', '$stock.minStockLevel'] }
            });

            // Get inventory value
            const inventoryValue = await Inventory.aggregate([
                { $match: { isDeleted: false } },
                { $group: {
                    _id: null,
                    totalValue: {
                        $sum: {
                            $multiply: [
                                { $toDouble: { $ifNull: ['$price.sellingPrice', 0] } },
                                { $ifNull: ['$stock.quantity', 0] }
                            ]
                        }
                    }
                }}
            ]);

            // Get category distribution
            const categoryDistribution = await Inventory.aggregate([
                { $match: { isDeleted: false } },
                { $group: {
                    _id: '$category',
                    count: { $sum: 1 }
                }},
                { $project: {
                    category: '$_id',
                    count: 1,
                    _id: 0
                }}
            ]);

            const stats = {
                totalCount,
                lowStockCount,
                inventoryValue: inventoryValue[0]?.totalValue || 0,
                categoryDistribution
            };

            return res.success(stats, req.__('INVENTORY_STATS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching inventory stats:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // New method for category distribution
    async getCategoryDistribution(req, res) {
        try {
            const distribution = await Inventory.aggregate([
                { $match: { isDeleted: false } },
                { $group: {
                    _id: '$category',
                    count: { $sum: 1 }
                }},
                { $project: {
                    category: '$_id',
                    count: 1,
                    _id: 0
                }}
            ]);

            return res.success(distribution, req.__('CATEGORY_DISTRIBUTION_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching category distribution:', error);
            return res.serverError('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new InventoryController();
