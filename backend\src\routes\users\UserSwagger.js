const { swagger } = require('../../util/swagger');

const userSchema = {
    type: 'object',
    properties: {
        category: {
            type: 'string',
            enum: ['gold', 'silver', 'diamond', 'platinum', 'gemstone', 'pearl', 'other'],
            description: 'Category of the inventory item'
        },
        name: {
            type: 'string',
            description: 'Name of the inventory item'
        },
        description: {
            type: 'string',
            description: 'Description of the inventory item'
        },
        type: {
            type: 'string',
            description: 'Type of the inventory item'
        },
        sku: {
            type: 'string',
            description: 'Stock Keeping Unit (unique identifier)'
        },
        purity: {
            type: 'string',
            enum: ['ss', '14', '18', '20', '22'],
            description: 'Purity level (required for gold and silver items)'
        },
        weight: {
            type: 'object',
            properties: {
                netWt: {
                    type: 'object',
                    properties: {
                        value: { type: 'number' },
                        unit: { type: 'string', enum: ['g', 'mg', 'ct', 'kg'] }
                    }
                },
                grossWt: {
                    type: 'object',
                    properties: {
                        value: { type: 'number' },
                        unit: { type: 'string', enum: ['g', 'mg', 'ct', 'kg'] }
                    }
                },
                stoneWt: {
                    type: 'object',
                    properties: {
                        value: { type: 'number' },
                        unit: { type: 'string', enum: ['g', 'mg', 'ct', 'kg'] }
                    }
                },
                diamondWt: {
                    type: 'object',
                    properties: {
                        value: { type: 'number' },
                        unit: { type: 'string', enum: ['g', 'mg', 'ct', 'kg'] }
                    }
                }
            }
        },
        price: {
            type: 'object',
            properties: {
                costPrice: { type: 'number', minimum: 0 },
                sellingPrice: { type: 'number', minimum: 0 },
                mrp: { type: 'number', minimum: 0 },
                makingCharges: { type: 'number', minimum: 0, default: 0 }
            }
        },
        stock: {
            type: 'object',
            properties: {
                quantity: { type: 'number', minimum: 0, default: 0 },
                minStockLevel: { type: 'number', minimum: 0, default: 1 }
            }
        },
        images: {
            type: 'array',
            items: { type: 'string', format: 'uri' }
        },
        qrCode: {
            type: 'string',
            description: 'QR code for the inventory item'
        },
        hasGST: {
            type: 'boolean',
            default: true
        },
        gstRate: {
            type: 'number',
            minimum: 0,
            maximum: 100,
            default: 3
        },
        tags: {
            type: 'array',
            items: { type: 'string' }
        },
        status: {
            type: 'string',
            enum: ['available', 'sold', 'reserved', 'damaged', 'in-maintenance'],
            default: 'available'
        },
        notes: {
            type: 'string',
            description: 'Additional notes about the inventory item'
        }
    }
};

const userSwagger = {
    tags: [
        {
            name: 'User',
            description: 'User management APIs'
        }
    ],
    paths: {
        '/api/inventory/public': {
            get: {
                tags: ['Inventory'],
                summary: 'Get public inventory list',
                description: 'Get a list of inventory items (public view)',
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'page',
                        in: 'query',
                        description: 'Page number',
                        schema: { type: 'integer', minimum: 1, default: 1 }
                    },
                    {
                        name: 'limit',
                        in: 'query',
                        description: 'Items per page',
                        schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
                    },
                    {
                        name: 'search',
                        in: 'query',
                        description: 'Search term',
                        schema: { type: 'string' }
                    },
                    {
                        name: 'category',
                        in: 'query',
                        description: 'Filter by category',
                        schema: { type: 'string', enum: ['gold', 'silver', 'diamond', 'platinum', 'gemstone', 'pearl', 'other'] }
                    }
                ],
                responses: {
                    200: {
                        description: 'Successful operation',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        items: {
                                            type: 'array',
                                            items: { $ref: '#/components/schemas/Inventory' }
                                        },
                                        pagination: {
                                            type: 'object',
                                            properties: {
                                                totalItems: { type: 'integer' },
                                                itemsPerPage: { type: 'integer' },
                                                totalPages: { type: 'integer' },
                                                currentPage: { type: 'integer' },
                                                hasNextPage: { type: 'boolean' },
                                                hasPrevPage: { type: 'boolean' }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/inventory/public/{id}': {
            get: {
                tags: ['Inventory'],
                summary: 'Get public inventory item',
                description: 'Get a single inventory item by ID (public view)',
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        schema: { type: 'string', format: 'objectId' }
                    }
                ],
                responses: {
                    200: {
                        description: 'Successful operation',
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/Inventory' }
                            }
                        }
                    }
                }
            }
        },
        '/api/inventory': {
            get: {
                tags: ['Inventory'],
                summary: 'Get inventory list',
                description: 'Get a list of inventory items with filters',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'page',
                        in: 'query',
                        description: 'Page number',
                        schema: { type: 'integer', minimum: 1, default: 1 }
                    },
                    {
                        name: 'limit',
                        in: 'query',
                        description: 'Items per page',
                        schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
                    },
                    {
                        name: 'search',
                        in: 'query',
                        description: 'Search term',
                        schema: { type: 'string' }
                    },
                    {
                        name: 'category',
                        in: 'query',
                        description: 'Filter by category',
                        schema: { type: 'string', enum: ['gold', 'silver', 'diamond', 'platinum', 'gemstone', 'pearl', 'other'] }
                    },
                    {
                        name: 'status',
                        in: 'query',
                        description: 'Filter by status',
                        schema: { type: 'string', enum: ['available', 'sold', 'reserved', 'damaged', 'in-maintenance'] }
                    },
                    {
                        name: 'minStock',
                        in: 'query',
                        description: 'Minimum stock level',
                        schema: { type: 'integer', minimum: 0 }
                    },
                    {
                        name: 'maxStock',
                        in: 'query',
                        description: 'Maximum stock level',
                        schema: { type: 'integer', minimum: 0 }
                    },
                    {
                        name: 'minPrice',
                        in: 'query',
                        description: 'Minimum price',
                        schema: { type: 'number', minimum: 0 }
                    },
                    {
                        name: 'maxPrice',
                        in: 'query',
                        description: 'Maximum price',
                        schema: { type: 'number', minimum: 0 }
                    }
                ],
                responses: {
                    200: {
                        description: 'Successful operation',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        items: {
                                            type: 'array',
                                            items: { $ref: '#/components/schemas/Inventory' }
                                        },
                                        pagination: {
                                            type: 'object',
                                            properties: {
                                                totalItems: { type: 'integer' },
                                                itemsPerPage: { type: 'integer' },
                                                totalPages: { type: 'integer' },
                                                currentPage: { type: 'integer' },
                                                hasNextPage: { type: 'boolean' },
                                                hasPrevPage: { type: 'boolean' }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            post: {
                tags: ['Inventory'],
                summary: 'Add inventory item',
                description: 'Add a new inventory item',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/Inventory' }
                        }
                    }
                },
                responses: {
                    201: {
                        description: 'Inventory item created successfully',
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/Inventory' }
                            }
                        }
                    }
                }
            }
        },
        '/api/inventory/bulk': {
            post: {
                tags: ['Inventory'],
                summary: 'Bulk add inventory items',
                description: 'Add multiple inventory items in a single request',
                security: [{ bearerAuth: [] }],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    items: {
                                        type: 'array',
                                        items: { $ref: '#/components/schemas/Inventory' }
                                    }
                                }
                            }
                        }
                    }
                },
                responses: {
                    201: {
                        description: 'Inventory items created successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: { $ref: '#/components/schemas/Inventory' }
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/inventory/{id}': {
            get: {
                tags: ['Inventory'],
                summary: 'Get inventory item',
                description: 'Get a single inventory item by ID',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        schema: { type: 'string', format: 'objectId' }
                    }
                ],
                responses: {
                    200: {
                        description: 'Successful operation',
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/Inventory' }
                            }
                        }
                    }
                }
            },
            put: {
                tags: ['Inventory'],
                summary: 'Update inventory item',
                description: 'Update an existing inventory item',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        schema: { type: 'string', format: 'objectId' }
                    }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/Inventory' }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Inventory item updated successfully',
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/Inventory' }
                            }
                        }
                    }
                }
            },
            delete: {
                tags: ['Inventory'],
                summary: 'Delete inventory item',
                description: 'Delete an inventory item',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        schema: { type: 'string', format: 'objectId' }
                    }
                ],
                responses: {
                    200: {
                        description: 'Inventory item deleted successfully'
                    }
                }
            }
        },
        '/api/inventory/{id}/stock': {
            patch: {
                tags: ['Inventory'],
                summary: 'Update stock quantity',
                description: 'Update the stock quantity of an inventory item',
                security: [{ bearerAuth: [] }],
                parameters: [
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        schema: { type: 'string', format: 'objectId' }
                    }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    quantity: { type: 'number', required: true },
                                    operation: { type: 'string', enum: ['add', 'set'], default: 'add' }
                                }
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Stock updated successfully',
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/Inventory' }
                            }
                        }
                    }
                }
            }
        },
        '/api/inventory/low-stock': {
            get: {
                tags: ['Inventory'],
                summary: 'Get low stock items',
                description: 'Get a list of inventory items with low stock',
                security: [{ bearerAuth: [] }],
                responses: {
                    200: {
                        description: 'Successful operation',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: { $ref: '#/components/schemas/Inventory' }
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/inventory/bulk-delete': {
            post: {
                tags: ['Inventory'],
                summary: 'Bulk delete inventory items',
                description: 'Delete multiple inventory items in a single request',
                security: [{ bearerAuth: [] }],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    ids: {
                                        type: 'array',
                                        items: { type: 'string', format: 'objectId' }
                                    }
                                }
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Inventory items deleted successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        deletedCount: { type: 'integer' }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    components: {
        schemas: {
            User: userSchema
        }
    }
};

module.exports = userSwagger;