import { formatMediaUrl } from 'utils/common';

/**
 * Reusable Label Design Component
 * This component generates the HTML structure for inventory labels
 * Uses the exact same design as the original PrintLabel.jsx
 *
 * @param {Object} item - The inventory item data
 * @param {string} size - Optional size override for the label
 * @returns {string} - HTML string for the label
 */
export const generateLabelHtml = (item, size = null) => {
  if (!item) return '';

  // Format size value to ensure special characters like inches (") are properly displayed
  const formatSizeValue = (sizeVal) => {
    if (!sizeVal) return "";
    // Replace inch symbol with HTML entity to ensure it displays correctly
    return sizeVal.toString().replace(/"/g, '&quot;');
  };

  // CSS styles for the label - EXACT copy from original PrintLabel.jsx
  const styles = `
    <style>
      @page { margin-left: 4px; margin-top:0px; margin-bottom:0px; margin-right:0px}
      body { margin: 0; padding-top:0px; padding-left:0px; padding-right:0px; padding-bottom:0px; }
      #label-banner {
        width: 60mm;
        height: 15mm;
        display: block;
        align-items: center;
        padding: 0;
        background: #fff;
        position: relative;
        font-family: Arial, sans-serif;
        padding:0px 5px 0px 5px;
        font-size:10.5px;
      }
      td {
        font-size:10.5px;
        line-height:13px;
      }
      p {
        margin:0px;
        margin-bottom:6px;
        font-size:11px;
      }
      .left-panel {
        width:45mm;
        float:left;
        padding-right:7px;
        border-right:dashed 1px black;
      }
      .right-panel {
        width:38px;
        float:left;
        padding-left:7px;
      }
      .label-part-left {
        width:50%;
        float:left;
      }
      .label-part-right {
        width:50%;
        float:left;
      }
      .main-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 55mm;
        height: 100%;
        padding: 2mm;
        border-right: 1px dashed #333;
      }
      .label-tail {
        position: absolute;
        right: -45mm;
        top: 0;
        width: 45mm;
        height: 15mm;
        background: #fff;
        border: 1px solid #333;
        border-left: none;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 2mm;
      }
      .tail-tip {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3mm;
        height: 3mm;
        background: #fff;
        border: 1px solid #333;
        border-radius: 1.5mm;
      }
      @media print {
        body { margin: 0; padding: 0; }
        #label-banner {
          box-shadow: none;
        }
      }
    </style>
  `;

  // Create the label content - EXACT copy from original PrintLabel.jsx
  const labelContent = `
    <div id="label-banner">
      <div style="font-size: 12px; line-height: 13px; padding: 2px;">
        <table width="100%" border="0" cellPadding="0" cellSpacing="0">
          <tr>
            <td width="50%">${item.name}</td>
            <td colSpan="2">${item.sku}${size ? ` (${formatSizeValue(size)})` : item.size ? ` (${formatSizeValue(item.size)})` : ""}</td>
          </tr>
          <tr>
            <td>
              ${item.weight?.grossWt?.value ? `
                GW: ${item.weight.grossWt.value} ${item.weight.grossWt.unit || "ct"}
                <br />
              ` : ''}
              ${item.weight?.netWt?.value ? `NW: ${item.weight.netWt.value} ${item.weight.netWt.unit || "ct"}` : ""}
              ${item.weight?.stoneWt?.value ? `${item.weight?.netWt?.value ? '<br />' : ''}SW: ${item.weight.stoneWt.value} ${item.weight.stoneWt.unit || "ct"}` : ""}
              ${item.weight?.diamondWt?.value ? `${(item.weight?.netWt?.value || item.weight?.stoneWt?.value) ? '<br />' : ''}
              DW: ${item.weight.diamondWt.value} ${item.weight.diamondWt.unit || "ct"}` : ""}
              ${item.weight?.averageWt?.value ? `${(item.weight?.netWt?.value || item.weight?.stoneWt?.value || item.weight?.diamondWt?.value) ? '<br />' : ''}
              AVG: ${item.weight.averageWt.value} ${item.weight.averageWt.unit || "ct"}` : ""}
            </td>
            <td>
              ${item.price?.costPrice ? `CP: ${item.price.costPrice} <br />` : ''}
              ${item.price?.sellingPrice ? `SP: $${item.price.sellingPrice} <br />` : ''}
            </td>
            <td rowSpan="2" width="18%">
              ${item.qrCode ? `
                <div style="width: 11mm; height: 100%; display: flex; align-items: center; justify-content: center;" class="right-panel">
                  <div style="width: 9mm; height: 9mm; border: 0.5px solid #ddd; display: flex; align-items: center; justify-content: center;">
                    <img src="${formatMediaUrl(item.qrCode)}" alt="QR" style="width: 8mm; height: 8mm; object-fit: contain; display: block;">
                  </div>
                </div>
              ` : ''}
            </td>
          </tr>
        </table>
      </div>
    </div>
  `;

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Print Label</title>
        ${styles}
      </head>
      <body>
        ${labelContent}
      </body>
    </html>
  `;
};

/**
 * Generate bulk labels HTML for multiple items
 * Uses the exact same design as single labels, just arranged for bulk printing
 * @param {Array} items - Array of inventory items
 * @returns {string} - Complete HTML for bulk printing
 */
export const generateBulkLabelsHtml = (items) => {
  if (!items || items.length === 0) return '';

  // Generate each label using the exact same design, but extract just the label content
  const labelContent = items.map((item) => {
    const singleLabelHtml = generateLabelHtml(item);
    // Extract the content between <div id="label-banner"> and </div>
    const labelMatch = singleLabelHtml.match(/<div id="label-banner">([\s\S]*?)<\/div>/);
    const content = labelMatch ? labelMatch[1] : '';

    // Wrap each label for bulk printing layout
    return `
      <div style="
        width: 60mm;
        height: 15mm;
        display: inline-block;
        align-items: center;
        padding: 0;
        background: #fff;
        position: relative;
        font-family: Arial, sans-serif;
        padding: 0px 5px 0px 5px;
        font-size: 10.5px;
        border: 1px solid #ddd;
        margin: 2mm;
        vertical-align: top;
        page-break-inside: avoid;
      " id="label-banner">
        ${content}
      </div>
    `;
  }).join('');

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Bulk Print Labels</title>
        <style>
          @page { margin-left: 4px; margin-top:0px; margin-bottom:0px; margin-right:0px}
          body { margin: 0; padding-top:0px; padding-left:0px; padding-right:0px; padding-bottom:0px; }
          td {
            font-size:10.5px;
            line-height:13px;
          }
          p {
            margin:0px;
            margin-bottom:6px;
            font-size:11px;
          }
          .left-panel {
            width:45mm;
            float:left;
            padding-right:7px;
            border-right:dashed 1px black;
          }
          .right-panel {
            width:38px;
            float:left;
            padding-left:7px;
          }
          .label-part-left {
            width:50%;
            float:left;
          }
          .label-part-right {
            width:50%;
            float:left;
          }
          @media print {
            body { margin: 0; padding: 0; }
            #label-banner {
              box-shadow: none;
            }
          }
        </style>
      </head>
      <body>
        ${labelContent}
      </body>
    </html>
  `;
};

export default {
  generateLabelHtml,
  generateBulkLabelsHtml
};
