import {
  Box,
  Switch,
  TextField,
  useTheme,
  Typography,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  RadioGroup,
  Radio,
  FormControlLabel,
  Checkbox,
  OutlinedInput,
  Chip,
  Card,
  CardContent,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  IconButton,
  List,
  ListItem,
  ListItemText,
} from "@mui/material";
import { LuxuryButton, JewelryFormField, PrintLabel, WeightField } from "components";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import { useFormik } from "formik";
import {
  useCreateInventoryMutation,
  useEditInventoryMutation,
  useGetInventoriesQuery,
} from "services";

import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import Header from "components/Header";

import {
  inventoryInitialValues,
  inventoryValidationSchema,
} from "constants/schemas";
import { toast } from "react-toastify";
import QRCode from "qrcode";
import { useUploadMediaMutation } from "services/utils.service";
import { checkGoldItems, checkOtherItems, formatMediaUrl } from "utils/common";
import { tokens } from "theme";

const CATEGORY_OPTIONS = [
  "gold",
  "silver",
  "diamond",
  "platinum",
  "gemstone",
  "pearl",
  "beats",
  "other",
];
const PURITY_OPTIONS = ["ss", "14", "18", "20", "22"];
const STATUS_OPTIONS = [
  "available",
  "sold",
  "reserved",
  "damaged",
  "in-maintenance",
];
const TAG_OPTIONS = [
  "bestseller",
  "new arrival",
  "discounted",
  "limited edition",
];

const Add = () => {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const [createInventory] = useCreateInventoryMutation();
  const [editInventory] = useEditInventoryMutation();
  const [uploadMedia] = useUploadMediaMutation();
  const { data: inventoryData } = useGetInventoriesQuery({ id }, { skip: !id });

  // Get clone data from location state
  const cloneData = location.state?.cloneData;
  const isClone = location.state?.isClone;

  // State for tracking form submission and modifications
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isFormModified, setIsFormModified] = useState(false);
  const [updatedInventory, setUpdatedInventory] = useState(null);



  function dataURLtoFile(dataUrl, fileName) {
    const [meta, base64] = dataUrl.split(",");
    const match = /data:(.*?);base64/.exec(meta);
    const mime = match ? match[1] : "image/png";
    const binary = window.atob(base64);
    const len = binary.length;
    const uint8Array = new Uint8Array(len);

    for (let i = 0; i < len; i += 1) {
      uint8Array[i] = binary.charCodeAt(i);
    }

    return new File([uint8Array], fileName, { type: mime });
  }

  const getQrCodeUrl = async (item_id) => {
    const QR_STORED_URL = `${window.location.origin}/inventory-banner?itemId=${item_id}`;
    const qrDataUrl = await QRCode.toDataURL(QR_STORED_URL, {
      errorCorrectionLevel: "H",
      margin: 2,
      scale: 8,
    });
    const qrFile = dataURLtoFile(qrDataUrl, "inventory-qr.png");

    const payload = {
      media: qrFile,
      type: "image",
      folder: "inventory",
    };

    const formData = new FormData();

    Object.entries(payload).forEach(([key, val]) => {
      formData.append(key, val);
    });

    try {
      const result = await uploadMedia(formData);
      if (result?.data?.url) {
        return result?.data?.url;
      }
    } catch (err) {
      console.log(err);
    }
  };

  const formik = useFormik({
    initialValues: {
      ...(cloneData || inventoryData || inventoryInitialValues),
      newSize: "", // For the size input field
      newSizeQuantity: "", // For the quantity input field
    },
    enableReinitialize: true,
    validationSchema: inventoryValidationSchema,
    onSubmit: async (values) => {
      try {
        if (!id) {
          const createRes = await createInventory(values).unwrap();
          if (!createRes?.success || !createRes?.data?._id) {
            throw new Error("Failed to create inventory");
          }
          const newId = createRes.data._id;

          const qrCodeUrl = await getQrCodeUrl(newId);

          await editInventory({
            id: newId,
            body: { ...values, qrCode: qrCodeUrl },
          }).unwrap();

          toast.success("Inventory created");
          navigate("/inventory");
          return;
        }

        const qrCodeUrl = await getQrCodeUrl(id);

        const updatedValues = { ...values, qrCode: qrCodeUrl };

        const result = await editInventory({
          id,
          body: updatedValues,
        }).unwrap();

        // Set the updated inventory data for the print label
        setUpdatedInventory(updatedValues);

        // Set form state
        setIsSubmitted(true);
        setIsFormModified(false);

        toast.success("Inventory updated");

        // Don't navigate away so user can print label
        // navigate("/inventory");
      } catch (err) {
        toast.error(
          err?.data?.message ?? err.message ?? "Something went wrong"
        );
      }
    },
  });

  const getError = (name) => formik.touched[name] && formik.errors[name];

  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  // Track form changes
  useEffect(() => {
    if (formik.dirty) {
      setIsFormModified(true);
    }
  }, [formik.values]);

  // Update main quantity based on sizes
  useEffect(() => {
    if (formik.values.sizes && formik.values.sizes.length > 0) {
      // Calculate total quantity from all sizes
      const totalQuantity = formik.values.sizes.reduce(
        (sum, size) => sum + (parseInt(size.quantity) || 0),
        0
      );

      // Update the main stock quantity
      formik.setFieldValue('stock.quantity', totalQuantity);
    }
  }, [formik.values.sizes]);



  // Custom styles for the form sections
  const sectionStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.08)",
      overflow: "hidden",
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      height: "100%",
      mb: 3,
      "&:hover": {
        transform: "translateY(-5px)",
        boxShadow: "0px 8px 30px rgba(0, 0, 0, 0.12)",
      },
    },
    sectionTitle: {
      fontFamily: '"Playfair Display", serif',
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: "relative",
      display: "inline-block",
      paddingBottom: "8px",
      "&::after": {
        content: '""',
        position: "absolute",
        width: "40%",
        height: "2px",
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
    formGrid: {
      display: "grid",
      gap: "20px",
      gridTemplateColumns: "repeat(2, 1fr)",
    },
  };

  const uploadImages = async (evt) => {
    const files = Array.from(evt.target.files);
    if (files.length > 0) {
      const uploadPromises = files.map(async (file) => {
        const payload = {
          media: file,
          type: "image",
          folder: "inventory",
        };

        const formData = new FormData();
        Object.entries(payload).forEach(([key, val]) => {
          formData.append(key, val);
        });

        try {
          const result = await uploadMedia(formData);
          return result?.data?.url;
        } catch (err) {
          console.log(err);
          return null;
        }
      });

      try {
        const uploadedUrls = await Promise.all(uploadPromises);
        const validUrls = uploadedUrls.filter(url => url !== null);

        // Add new images to existing ones
        const currentImages = formik.values.images || [];
        formik.setFieldValue("images", [...currentImages, ...validUrls]);
      } catch (err) {
        console.log(err);
      }
    }
  };

  const removeImage = (indexToRemove) => {
    const currentImages = formik.values.images || [];
    const updatedImages = currentImages.filter((_, index) => index !== indexToRemove);
    formik.setFieldValue("images", updatedImages);
  };

  return (
    <Box m="20px">
      <Header
        title={`${isClone ? "CLONE" : id ? "EDIT" : "CREATE"} INVENTORY`}
        subtitle={isClone ? "Clone inventory item with new SKU" : "Manage your jewelry inventory with detailed information"}
      />

      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          {/* BASIC INFORMATION SECTION */}
          <Grid item xs={12}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Basic Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box sx={sectionStyles.formGrid}>
                  {/* CATEGORY */}
                  <JewelryFormField
                    type="select"
                    label="Category"
                    name="category"
                    value={formik.values.category}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("category")}
                    helperText={getError("category")}
                    options={CATEGORY_OPTIONS.map((option) => ({
                      value: option,
                      label: option.charAt(0).toUpperCase() + option.slice(1),
                    }))}
                    required
                  />

                  {/* NAME */}
                  <JewelryFormField
                    label="Item Name"
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("name")}
                    helperText={getError("name")}
                    required
                  />

                  {/* SKU */}
                  <JewelryFormField
                    label="SKU*"
                    name="sku"
                    value={formik.values.sku}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("sku")}
                    helperText={isClone ? "Enter a new unique SKU for the cloned item" : getError("sku")}
                    required
                  />

                  {/* SIZES */}
                  <Box sx={{ gridColumn: "1 / -1" }}>
                    <Typography
                      variant="h6"
                      sx={{ mb: 2, color: colors.blackAccent[700] }}
                    >
                      Sizes
                    </Typography>

                    <List
                      sx={{
                        border: `1px solid ${colors.primary[200]}`,
                        borderRadius: "8px",
                        mb: 2,
                        maxHeight: "200px",
                        overflow: "auto",
                      }}
                    >
                      {formik.values.sizes.length === 0 ? (
                        <ListItem>
                          <ListItemText
                            primary="No sizes added yet"
                            primaryTypographyProps={{
                              color: colors.blackAccent[500],
                              fontStyle: "italic",
                            }}
                          />
                        </ListItem>
                      ) : (
                        formik.values.sizes.map((size, index) => (
                          <ListItem
                            key={index}
                            secondaryAction={
                              <IconButton
                                edge="end"
                                aria-label="delete"
                                onClick={() => {
                                  const newSizes = [...formik.values.sizes];
                                  newSizes.splice(index, 1);
                                  formik.setFieldValue("sizes", newSizes);
                                }}
                                sx={{
                                  color: colors.redAccent[500],
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            }
                            sx={{
                              borderBottom:
                                index < formik.values.sizes.length - 1
                                  ? `1px solid ${colors.primary[200]}`
                                  : "none",
                            }}
                          >
                            <ListItemText
                              primary={`Size: ${size.value}`}
                              secondary={`Quantity: ${size.quantity}`}
                            />
                          </ListItem>
                        ))
                      )}
                    </List>

                    <Box
                      sx={{
                        display: "flex",
                        gap: 2,
                        alignItems: "flex-start",
                      }}
                    >
                      <TextField
                        label="Size"
                        variant="outlined"
                        size="small"
                        value={formik.values.newSize || ""}
                        onChange={(e) =>
                          formik.setFieldValue("newSize", e.target.value)
                        }
                        sx={{ flex: 1 }}
                      />
                      <TextField
                        label="Quantity"
                        variant="outlined"
                        type="number"
                        size="small"
                        value={formik.values.newSizeQuantity || ""}
                        onChange={(e) =>
                          formik.setFieldValue(
                            "newSizeQuantity",
                            e.target.value
                          )
                        }
                        inputProps={{ min: 0 }}
                        sx={{ flex: 1 }}
                      />
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => {
                          if (
                            formik.values.newSize &&
                            formik.values.newSizeQuantity
                          ) {
                            const newSizes = [
                              ...formik.values.sizes,
                              {
                                value: formik.values.newSize,
                                quantity: Number(formik.values.newSizeQuantity),
                              },
                            ];
                            formik.setFieldValue("sizes", newSizes);
                            formik.setFieldValue("newSize", "");
                            formik.setFieldValue("newSizeQuantity", "");
                          }
                        }}
                        sx={{
                          backgroundColor: colors.primary[500],
                          color: colors.whiteAccent[100],
                          "&:hover": {
                            backgroundColor: colors.primary[600],
                          },
                        }}
                      >
                        Add Size
                      </Button>
                    </Box>
                  </Box>

                  {/* PURITY - Conditional */}
                  {checkGoldItems(formik.values.category) && (
                    <JewelryFormField
                      type="select"
                      label="Purity"
                      name="purity"
                      value={formik.values.purity}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={!!getError("purity")}
                      helperText={getError("purity")}
                      options={PURITY_OPTIONS.map((option) => ({
                        value: option,
                        label: option,
                      }))}
                      required
                    />
                  )}

                  {/* TAGS MULTI SELECT */}
                  <Box sx={{ gridColumn: "1 / -1" }}>
                    <FormControl fullWidth>
                      <InputLabel>Tags</InputLabel>
                      <Select
                        multiple
                        name="tags"
                        value={formik.values.tags}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        input={<OutlinedInput label="Tags" />}
                        renderValue={(selected) => (
                          <Box
                            sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}
                          >
                            {selected.map((value) => (
                              <Chip
                                key={value}
                                label={value}
                                sx={{
                                  backgroundColor: colors.primary[100],
                                  color: colors.primary[700],
                                  fontWeight: 500,
                                  borderRadius: "4px",
                                }}
                              />
                            ))}
                          </Box>
                        )}
                      >
                        {TAG_OPTIONS.map((tag) => (
                          <MenuItem key={tag} value={tag}>
                            <Checkbox
                              checked={formik.values.tags?.indexOf(tag) > -1}
                            />
                            {tag.charAt(0).toUpperCase() + tag.slice(1)}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  {/* STATUS RADIO */}
                  <Box sx={{ gridColumn: "1 / -1" }}>
                    <FormControl
                      component="fieldset"
                      error={!!getError("status")}
                      fullWidth
                    >
                      <Typography
                        variant="h6"
                        sx={{ mb: 1, color: colors.blackAccent[700] }}
                      >
                        Status
                      </Typography>
                      <RadioGroup
                        row
                        name="status"
                        value={formik.values.status}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                      >
                        {STATUS_OPTIONS.map((option) => (
                          <FormControlLabel
                            key={option}
                            value={option}
                            control={
                              <Radio
                                sx={{
                                  color: colors.primary[400],
                                  "&.Mui-checked": {
                                    color: colors.primary[500],
                                  },
                                }}
                              />
                            }
                            label={
                              option.charAt(0).toUpperCase() + option.slice(1)
                            }
                            sx={{ textTransform: "capitalize" }}
                          />
                        ))}
                      </RadioGroup>
                      <Typography variant="caption" color="error">
                        {getError("status")}
                      </Typography>
                    </FormControl>
                  </Box>

                  {/* ITEM IMAGES */}
                  <Box sx={{ gridColumn: "1 / -1" }}>
                    <FormControl fullWidth>
                      <Typography
                        variant="h6"
                        sx={{ mb: 1, color: colors.blackAccent[700] }}
                      >
                        Item Images
                      </Typography>
                      <OutlinedInput
                        type="file"
                        name="images"
                        onChange={uploadImages}
                        inputProps={{
                          accept: "image/*",
                          multiple: true
                        }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                        You can select multiple images at once
                      </Typography>
                      <Typography variant="caption" color="error">
                        {getError("media")}
                      </Typography>

                      {/* Preview uploaded images */}
                      {formik.values.images?.length > 0 && (
                        <Box mt={2}>
                          <Typography variant="subtitle2" sx={{ mb: 1, color: colors.blackAccent[700] }}>
                            Uploaded Images ({formik.values.images.length})
                          </Typography>
                          <Box sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 2,
                            maxHeight: '300px',
                            overflowY: 'auto',
                            p: 1,
                            border: `1px solid ${colors.primary[200]}`,
                            borderRadius: '8px'
                          }}>
                            {formik.values.images.map((imageUrl, index) => (
                              <Box
                                key={index}
                                sx={{
                                  position: 'relative',
                                  width: '120px',
                                  height: '120px',
                                  borderRadius: '8px',
                                  overflow: 'hidden',
                                  border: `2px solid ${colors.primary[300]}`,
                                }}
                              >
                                <img
                                  src={formatMediaUrl(imageUrl)}
                                  alt={`Preview ${index + 1}`}
                                  style={{
                                    width: "100%",
                                    height: "100%",
                                    objectFit: "cover",
                                  }}
                                />
                                <IconButton
                                  size="small"
                                  onClick={() => removeImage(index)}
                                  sx={{
                                    position: 'absolute',
                                    top: 4,
                                    right: 4,
                                    backgroundColor: colors.redAccent[500],
                                    color: 'white',
                                    '&:hover': {
                                      backgroundColor: colors.redAccent[600],
                                    },
                                    width: 24,
                                    height: 24,
                                  }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            ))}
                          </Box>
                        </Box>
                      )}
                    </FormControl>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* WEIGHT & MEASUREMENTS SECTION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Weight & Measurements
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box
                  sx={{
                    display: "grid",
                    gap: "20px",
                    gridTemplateColumns: "1fr",
                  }}
                >
                  {/* WEIGHT FIELDS (conditional) */}
                  {checkGoldItems(formik.values.category) ? (
                    <>
                      {["grossWt", "netWt", "stoneWt", "diamondWt"].map(
                        (field) => (
                          <WeightField
                            key={field}
                            label={field.replace("Wt", " Weight")}
                            name={`weight.${field}.value`}
                            unitName={`weight.${field}.unit`}
                            value={formik.values.weight?.[field]?.value || ""}
                            unit={formik.values.weight?.[field]?.unit || "ct"}
                            onChange={(e) => {
                              const value = e.target.value;
                              // For stoneWt and diamondWt, allow alphanumeric values
                              if (["stoneWt", "diamondWt"].includes(field)) {
                                // Allow any value for alphanumeric fields
                                formik.setFieldValue(`weight.${field}.value`, value);
                              } else {
                                // For other fields, only allow numbers and decimals
                                if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                                  formik.setFieldValue(`weight.${field}.value`, value);
                                }
                              }
                            }}
                            onUnitChange={(e) => {
                              formik.setFieldValue(`weight.${field}.unit`, e.target.value);
                            }}
                            onBlur={formik.handleBlur}
                            error={!!getError(`weight.${field}.value`)}
                            helperText={getError(`weight.${field}.value`)}
                            placeholder={["stoneWt", "diamondWt"].includes(field)
                              ? "Enter value (e.g. 0.01 or 2-3ct)"
                              : "Enter decimal value (e.g. 0.01)"}
                            alphanumeric={["stoneWt", "diamondWt"].includes(field)}
                          />
                        )
                      )}
                    </>
                  ) : formik.values.category === "diamond" ? (
                    <>
                      <WeightField
                        label="Gross Weight"
                        name="weight.grossWt.value"
                        unitName="weight.grossWt.unit"
                        value={formik.values.weight?.grossWt?.value || ""}
                        unit={formik.values.weight?.grossWt?.unit || "ct"}
                        onChange={(e) => {
                          // Only allow numbers and decimals
                          const value = e.target.value;
                          if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                            formik.setFieldValue("weight.grossWt.value", value);
                          }
                        }}
                        onUnitChange={(e) => {
                          formik.setFieldValue("weight.grossWt.unit", e.target.value);
                        }}
                        onBlur={formik.handleBlur}
                        error={!!getError("weight.grossWt.value")}
                        helperText={getError("weight.grossWt.value")}
                        placeholder="Enter decimal value (e.g. 0.01)"
                      />
                      <WeightField
                        label="Net Weight"
                        name="weight.netWt.value"
                        unitName="weight.netWt.unit"
                        value={formik.values.weight?.netWt?.value || ""}
                        unit={formik.values.weight?.netWt?.unit || "ct"}
                        onChange={(e) => {
                          // Only allow numbers and decimals
                          const value = e.target.value;
                          if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                            formik.setFieldValue("weight.netWt.value", value);
                          }
                        }}
                        onUnitChange={(e) => {
                          formik.setFieldValue("weight.netWt.unit", e.target.value);
                        }}
                        onBlur={formik.handleBlur}
                        error={!!getError("weight.netWt.value")}
                        helperText={getError("weight.netWt.value")}
                        placeholder="Enter decimal value (e.g. 0.01)"
                      />
                    </>
                  ) : (
                    <WeightField
                      label="Gross Weight"
                      name="weight.grossWt.value"
                      unitName="weight.grossWt.unit"
                      value={formik.values.weight?.grossWt?.value || ""}
                      unit={formik.values.weight?.grossWt?.unit || "ct"}
                      onChange={(e) => {
                        // Only allow numbers and decimals
                        const value = e.target.value;
                        if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          formik.setFieldValue("weight.grossWt.value", value);
                        }
                      }}
                      onUnitChange={(e) => {
                        formik.setFieldValue("weight.grossWt.unit", e.target.value);
                      }}
                      onBlur={formik.handleBlur}
                      error={!!getError("weight.grossWt.value")}
                      helperText={getError("weight.grossWt.value")}
                      placeholder="Enter decimal value (e.g. 0.01)"
                    />
                  )}
                  <WeightField
                    label="Average Weight"
                    name="weight.averageWt.value"
                    unitName="weight.averageWt.unit"
                    value={formik.values.weight?.averageWt?.value || ""}
                    unit={formik.values.weight?.averageWt?.unit || "ct"}
                    onChange={(e) => {
                      // Allow alphanumeric values for Average Weight
                      const value = e.target.value;
                      formik.setFieldValue("weight.averageWt.value", value);
                    }}
                    onUnitChange={(e) => {
                      formik.setFieldValue("weight.averageWt.unit", e.target.value);
                    }}
                    onBlur={formik.handleBlur}
                    error={!!getError("weight.averageWt.value")}
                    helperText={getError("weight.averageWt.value")}
                    placeholder="Enter value (e.g. 0.01 or 2-3ct)"
                    alphanumeric={true}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* PRICING SECTION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Pricing Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box
                  sx={{
                    display: "grid",
                    gap: "20px",
                    gridTemplateColumns: "1fr",
                  }}
                >
                  {/* PRICE FIELDS */}
                  <JewelryFormField
                    type="text"
                    label="Cost Price"
                    name="price.costPrice"
                    value={formik.values.price.costPrice}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("price.costPrice")}
                    helperText={getError("price.costPrice")}
                    placeholder="Enter cost price without $ symbol"
                  />

                  <JewelryFormField
                    type="text"
                    label="Selling Price"
                    name="price.sellingPrice"
                    value={formik.values.price.sellingPrice}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("price.sellingPrice")}
                    helperText={getError("price.sellingPrice")}
                    placeholder="Enter price or value like 'TBD', 'SP50'"
                  />

                  <JewelryFormField
                    type="text"
                    label="MRP"
                    name="price.mrp"
                    value={formik.values.price.mrp}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter price or alphanumeric value"
                  />

                  {checkOtherItems(formik.values.category) && (
                    <JewelryFormField
                      type="number"
                      label="Making Charges"
                      name="price.makingCharges"
                      value={formik.values.price.makingCharges}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      inputProps={{ step: "0.01" }}
                    />
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* INVENTORY MANAGEMENT SECTION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Inventory Management
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box
                  sx={{
                    display: "grid",
                    gap: "20px",
                    gridTemplateColumns: "1fr",
                  }}
                >
                  {/* STOCK FIELDS */}
                  <JewelryFormField
                    type="number"
                    label="Quantity"
                    name="stock.quantity"
                    value={formik.values.stock.quantity}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    required
                    disabled={formik.values.sizes && formik.values.sizes.length > 0}
                    helperText={formik.values.sizes && formik.values.sizes.length > 0
                      ? "Auto-calculated from sizes"
                      : ""}
                    InputProps={{
                      readOnly: formik.values.sizes && formik.values.sizes.length > 0,
                    }}
                  />

                  <JewelryFormField
                    type="number"
                    label="Min Stock Level"
                    name="stock.minStockLevel"
                    value={formik.values.stock.minStockLevel}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    min={formik.values.stock.quantity}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* ADDITIONAL INFO SECTION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Additional Info
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box
                  sx={{
                    display: "grid",
                    gap: "20px",
                    gridTemplateColumns: "1fr",
                  }}
                >
                  {/* NOTES */}
                  <JewelryFormField
                    type="textarea"
                    label="Notes"
                    name="notes"
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    rows={4}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* QR CODE SECTION - Only show if editing and QR code exists */}
          {formik.values.qrCode && id && (
            <Grid item xs={12}>
              <Card sx={sectionStyles.card}>
                <CardContent>
                  <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                    QR Code
                  </Typography>
                  <Divider sx={sectionStyles.divider} />

                  <Box
                    sx={{ display: "flex", justifyContent: "center", mt: 2 }}
                  >
                    <Paper
                      elevation={3}
                      sx={{ p: 2, borderRadius: "12px", overflow: "hidden" }}
                    >
                      <img
                        src={formatMediaUrl(formik.values.qrCode)}
                        alt="QR Code"
                        width={200}
                        height={200}
                        style={{ display: "block" }}
                      />
                    </Paper>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>

        {/* SUBMIT BUTTON */}
        <Box display="flex" justifyContent="space-between" mt="30px">
          <LuxuryButton
            onClick={() => navigate("/inventory")}
            size="large"
            variant="outlined"
          >
            Back to Inventory
          </LuxuryButton>

          <Box display="flex" gap={2}>
            {id && isSubmitted && !isFormModified && (
              <PrintLabel
                item={updatedInventory}
                buttonSize="large"
                buttonVariant="outlined"
                buttonSx={{ mt: 0 }}
              />
            )}
            <LuxuryButton type="submit" size="large">
              {id ? "Update Inventory" : "Create Inventory"}
            </LuxuryButton>
          </Box>
        </Box>


      </form>
    </Box>
  );
};

export default Add;
