const axios = require('axios');


const SHIPROCKET_API_URL = process.env.SHIPROCKET_API_URL;

// Middleware to get Shiprocket Token
const getShiprocketToken = async (req, res, next) => {
    try {
        // Check if the token exists in Redis
        // const token = await client.get('shiprocket_token');
        const token =''
        if (token) {
            req.shiprocketToken = token; // Attach token to the request object
            next(); // Proceed to the next middleware or controller
        } else {
            // Fetch a new token if not in Redis
            const response = await axios.post(`${SHIPROCKET_API_URL}/auth/login`, {
                email: process.env.SHIPROCKET_EMAIL,
                password: process.env.SHIPROCKET_PASSWORD,
            });

            const newToken = response.data.token;

            // Store token in Redis with an expiration (e.g., 10 hours)
            // await client.setEx('shiprocket_token', 36000, newToken);
console.log("newToken >>>",newToken)
            req.shiprocketToken = newToken; // Attach the new token to the request
            next(); // Proceed to the next middleware or controller
        }
    } catch (error) {
        console.error('Failed to get Shiprocket token:', error);
        return res.status(500).json({ error: 'INTERNAL_SERVER_ERROR' });
    }
};
const getShiprocketTokenInternal = async (req, res, next) => {
    try {
        // Check if the token exists in Redis
        // const token = await client.get('shiprocket_token');
        const token =''
        if (token) {
            req.shiprocketToken = token; // Attach token to the request object
            next(); // Proceed to the next middleware or controller
        } else {
            // Fetch a new token if not in Redis
            const response = await axios.post(`${SHIPROCKET_API_URL}/auth/login`, {
                email: process.env.SHIPROCKET_EMAIL,
                password: process.env.SHIPROCKET_PASSWORD,
            });

           return newToken = response.data.token;

            // Store token in Redis with an expiration (e.g., 10 hours)
            // await client.setEx('shiprocket_token', 36000, newToken);

        }
    } catch (error) {
        console.error('Failed to get Shiprocket token:', error);
        return res.status(500).json({ error: 'INTERNAL_SERVER_ERROR' });
    }
};


const makeApiRequest = async (endpoint, method, data) => {
    try {
        const API_TOKEN = await getShiprocketTokenInternal()
        const response = await axios({
            url: `${SHIPROCKET_API_URL}${endpoint}`,
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_TOKEN}`
            },
            data
        });
        return response.data;
    } catch (error) {
        console.error(`Error in ${endpoint}:`, error.response.data);
        throw error;
    }
};

// Function to create a new shipment
async function createShipment(orderDetails) {
    const token = await getShiprocketToken();
    try {
        const response = await axios.post(`${SHIPROCKET_API_URL}/orders/create/adhoc`, orderDetails, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error creating shipment:', error);
        throw error;
    }
}

// Function to track an order
async function trackOrder(shipmentId) {
    const token = await getShiprocketToken();
    try {
        const response = await axios.get(`${SHIPROCKET_API_URL}/courier/track/shipment/${shipmentId}`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error tracking order:', error);
        throw error;
    }
}

// Function to check pincode availability
async function checkPincodeAvailability(pincode) {
    const token = await getShiprocketToken();
    try {
        const response = await axios.get(`${SHIPROCKET_API_URL}/courier/serviceability/`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
            params: {
                pickup_postcode: '110001',  // Your warehouse pincode
                delivery_postcode: pincode,
                cod: 1,  // 1 for COD available, 0 otherwise
                weight: 0.5  // Default weight (can be adjusted as needed)
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error checking pincode availability:', error);
        throw error;
    }
}

// Function to get an invoice
async function getInvoice(shipmentId) {
    const token = await getShiprocketToken();
    try {
        const response = await axios.get(`${SHIPROCKET_API_URL}/invoices/${shipmentId}`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error getting invoice:', error);
        throw error;
    }
}

// Function to calculate shipping cost
async function getShippingCost(shippingDetails) {
    const token = await getShiprocketToken();
    try {
        const response = await axios.post(`${SHIPROCKET_API_URL}/courier/serviceability/`, shippingDetails, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error calculating shipping cost:', error);
        throw error;
    }
}

// Function to cancel, return, or replace an order
async function manageOrder(orderId, action) {
    const token = await getShiprocketToken();
    try {
        const response = await axios.post(`${SHIPROCKET_API_URL}/orders/${orderId}/${action}`, null, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error(`Error performing ${action} on order:`, error);
        throw error;
    }
}

// Export the functions for use in other parts of your application
module.exports = {
    createShipment,
    trackOrder,
    checkPincodeAvailability,
    getInvoice,
    getShippingCost,
    manageOrder,
    getShiprocketToken,
    makeApiRequest
};
