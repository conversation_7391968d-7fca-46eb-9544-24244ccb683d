import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import EyeIcon from "@mui/icons-material/Visibility";
import { Box, Button, IconButton, InputAdornment, Stack, TextField, Tooltip, Typography, useMediaQuery, useTheme } from "@mui/material";
import Header from "components/Header";
import StyledDataGrid from "components/datagrids/StyledDataGrid";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useDeleteCustomerMutation, useGetCustomersQuery } from "services";
import { setConfirmModalConfig } from "store/slices/utilSlice";
import { tokens } from "theme";
import dataGridStyles from "../../styles/dataGridStyles";
import { useDebounce, useDeleteRecord } from "hooks";
import { Search } from "@mui/icons-material";

const Customers = () => {
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(100);
  const [searchParams, setSearchParams] = useSearchParams();
  const searchName = searchParams.get("name") || "";
  const debouncedSearch = useDebounce(searchName, 500);
  const [filterVisible, setFilterVisible] = useState(false);
  const dispatch = useDispatch();
  const userInfo = useSelector((state) => state.auth.user);
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const styles = dataGridStyles(theme.palette.mode);
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // queries
  // const { data: {customers: customersData = []} = [], isLoading, refetch } = useGetCustomersQuery(
  //   {
  //     params: {
  //       page: page + 1,
  //       limit: pageSize,
  //     },
  //   },
  //   {
  //     refetchOnMountOrArgChange: true,
  //   }
  // );

  const {
    data = {},
    isLoading,
    refetch,
  } = useGetCustomersQuery(
    {
      params: {
        page: page + 1,
        limit: pageSize,
        search: debouncedSearch ?? undefined,
      },
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );
  const { items: customersData = [], pagination = {} } = data ?? {};
  const [deleteCustomer] = useDeleteCustomerMutation();
  const { deleteRecord } = useDeleteRecord(deleteCustomer);



  const handleSearchChange = (evt) => {
    setSearchParams({
      name: evt.target.value,
    });
  };

  const handleClearSearch = () => {
    setSearchParams({});
  };

  const toggleFilterVisibility = () => {
    setFilterVisible(!filterVisible);
  };

  const columns = [
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      cellClassName: "name-column--cell",
      renderCell: (params) => {
        return (
          <span
            style={{
              textTransform: "capitalize",
            }}
          >
            {params.row.name}
          </span>
        );
      },
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      cellClassName: "name-column--cell",
    },
    {
      field: "phone",
      headerName: "Contact",
      flex: 1,
      cellClassName: "name-column--cell",
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <Stack direction="row" spacing={1} justifyContent="center">
          {userInfo?.role === "ADMIN" && (
            <>
              <Tooltip title="Edit">
                <IconButton
                  component={Link}
                  to={`/customers/edit/${params.row._id}`}
                  size="small"
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.primary[500] }
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  size="small"
                  onClick={() => {
                    const payload = {
                      id: params.row._id,
                    };
                    dispatch(
                      setConfirmModalConfig({
                        visible: true,
                        data: {
                          onSubmit: () => deleteRecord(payload, refetch),
                          content: {
                            heading: "Delete Customer?",
                            description:
                              "Are you sure you want to delete this customer?",
                          },
                        },
                      })
                    );
                  }}
                  sx={{
                    color: colors.blackAccent[700],
                    '&:hover': { color: colors.redAccent[500] }
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </>
          )}

          <Tooltip title="View Details">
            <IconButton
              component={Link}
              to={`/customers/${params.row._id}`}
              size="small"
              sx={{
                color: colors.blackAccent[700],
                '&:hover': { color: colors.primary[500] }
              }}
            >
              <EyeIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      ),
    }
  ];

  return (
    <Box m="20px">
      <Box
        sx={{
          ...styles.headingsContainer,
          ...styles.dFlex,
          ...styles.alignItemsStart,
          ...styles.justifyContentBetween,
        }}
      >
        <Box sx={styles.mainHeadings}>
          <Header title="CUSTOMERS" subtitle="Managing the Customers" />
        </Box>
          <Box>
            <Button
              sx={styles.buttonMd}
              LinkComponent={Link}
              to="/customers/add"
            >
              <AddOutlinedIcon sx={{ mr: "10px" }} />
              Add Customer
            </Button>
          </Box>
      </Box>
      <Box
        sx={{
          ...styles.formContainer,
          p: { xs: 1, sm: 2 },
          borderRadius: '12px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'stretch', sm: 'center' },
            justifyContent: 'space-between',
            gap: 2,
            mb: 2,
          }}
        >
          <TextField
            placeholder="Search by name, SKU, or description"
            onChange={handleSearchChange}
            value={searchName}
            fullWidth
            size={isMobile ? "small" : "medium"}
            sx={{
              maxWidth: { sm: '400px' },
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: searchName && (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear search"
                    onClick={handleClearSearch}
                    edge="end"
                    size="small"
                  >
                    <Clear fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <Box sx={{ display: { xs: 'none', md: 'block' } }}>
            <Typography variant="body2" color="text.secondary">
              {pagination.totalItems || 0} items found
            </Typography>
          </Box>
        </Box>
        <StyledDataGrid
          rows={customersData}
          columns={columns}
          getRowId={(row) => row._id}
          loading={isLoading}
          pagination
          page={page}
          pageSize={pageSize}
          rowCount={pagination.totalItems || 0}
          paginationMode="server"
          onPageChange={(newPage) => setPage(newPage)}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          className="no-shadow"
          noResultText="No inventory items found."
          enableFiltering={true}
          enableColumnVisibilityToggle={true}
          enableDensitySelector={true}
          enableExport={true}
          containerStyle={{
            borderRadius: '8px',
            height: isMobile ? '60vh' : isTablet ? '65vh' : '70vh',
          }}
          customStyle={{
            '& .MuiDataGrid-toolbarContainer': {
              padding: isMobile ? '8px 4px' : '16px 8px',
              flexWrap: 'wrap',
              gap: '8px',
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'name', sort: 'asc' }],
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default Customers;
