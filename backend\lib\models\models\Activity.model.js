const mongoose = require('mongoose');

const activitySchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    actionType: {
      type: String,
      enum: ['create', 'update', 'delete', 'login', 'logout', 'view', 'export', 'print', 'other'],
      required: true,
    },
    module: {
      type: String,
      enum: [
        'inventory',
        'invoice',
        'customer',
        'measurement',
        'label',
        'qrcode',
        'report',
        'settings',
        'user',
        'auth',
        'dashboard',
      ],
      required: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    details: {
      type: mongoose.Schema.Types.Mixed,
    },
    itemId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster searching
activitySchema.index({ user: 1, module: 1, actionType: 1 });
activitySchema.index({ createdAt: -1 });

// Check if the model has already been compiled
const Activity = mongoose.models.Activity || mongoose.model('Activity', activitySchema);

module.exports = Activity;


