const express = require('express');
const router = express.Router();
const AdminController = require('./AdminController');
const { verifyTokenAdmin } = require('../../util/auth');
const { checkPermission } = require('../../util/permissions');
const { Permissions } = require('../../../lib/models/enums');
const validations = require('./AdminValidations');
const { validate } = require('../../util/validations');

// Get all available permissions and their role mappings
router.get(
    '/permissions',
    verifyTokenAdmin,
    AdminController.getAllPermissions
);

// Get user permissions based on their role
router.get(
    '/users/:id/permissions',
    verifyTokenAdmin,
    checkPermission(Permissions.MANAGE_PERMISSIONS),
    AdminController.getUserPermissions
);

router.post('/log-in', validate(validations.logIn), AdminController.logIn);
router.get('/log-out', AdminController.logout);

module.exports = router;
