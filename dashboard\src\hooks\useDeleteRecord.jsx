import { toast } from "react-toastify";

const useDeleteRecord = (deleteRow) => {
  const deleteRecord = async (payload, refetch) => {
    try {
      const res = await deleteRow(payload);
      if (res?.success) {
        toast.success(res?.message);
        refetch();
      }
    } catch (error) {
      console.error(error);
    }
  };

  return { deleteRecord };
};

export default useDeleteRecord;
