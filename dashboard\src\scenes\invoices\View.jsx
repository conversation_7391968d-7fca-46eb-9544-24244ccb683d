import React, { useRef, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useGetInvoicesQuery } from "../../services/invoices.service";
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
  Stack,
  IconButton,
  Tooltip,
  Paper,
  Snackbar,
  Alert,
  TextField,
  MenuItem,
} from "@mui/material";
// Removed date picker imports to use simple TextField instead
import LuxuryButton from "components/LuxuryButton";
import Header from "components/Header";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import PrintIcon from "@mui/icons-material/Print";
import EmailIcon from "@mui/icons-material/Email";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import DownloadIcon from "@mui/icons-material/Download";
import PhoneIcon from "@mui/icons-material/Phone";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import LanguageIcon from "@mui/icons-material/Language";
import BusinessIcon from "@mui/icons-material/Business";
import VerifiedIcon from "@mui/icons-material/Verified";
import { formatMediaUrl } from "utils/common";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

const ViewInvoice = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const invoiceRef = useRef(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: "", severity: "success" });

  // Invoice term options
  const termOptions = ["Net", "Net 15", "Net 30", "Net 60", "Net 90"];

  // Ship via options
  const shipViaOptions = ["UPS", "FedEx", "DHL", "USPS"];

  // Default shipping date to current date
  const [shippingDate, setShippingDate] = useState(new Date());

  // Luxury jewelry color scheme
  const colors =
    theme.palette.mode === "dark"
      ? {
          primary: {
            500: "#C9A063", // Gold
            400: "#D4B77B", // Lighter gold
            100: "#F9F7F4", // Soft white
            700: "#B08D57", // Darker gold
          },
          blackAccent: { 900: "#191919", 700: "#7D7D7D", 500: "#A9A9A9" }, // Accent gray shades
        }
      : {
          primary: {
            500: "#C9A063", // Gold
            400: "#D4B77B", // Lighter gold
            100: "#F9F7F4", // Soft white
            700: "#B08D57", // Darker gold
          },
          blackAccent: { 900: "#191919", 700: "#7D7D7D", 500: "#A9A9A9" }, // Accent gray shades
        };
  const { data = {} } = useGetInvoicesQuery({ id }, { skip: !id });
  const invoice = data.invoice || {};

  const viewStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 8px 30px rgba(201, 160, 99, 0.08)",
      border: `1px solid ${colors.primary[400]}`,
      background: theme.palette.mode === "dark" ? "#232323" : colors.primary[100],
      overflow: "hidden",
      mb: 3,
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      "&:hover": {
        boxShadow: "0px 10px 40px rgba(201, 160, 99, 0.12)",
        transform: "translateY(-2px)",
      },
    },
    sectionTitle: {
      fontFamily: "Playfair Display, serif",
      color: colors.primary[500],
      fontWeight: 600,
      mb: 1.5,
      letterSpacing: 1.2,
      position: "relative",
      display: "inline-block",
      "&::after": {
        content: '""',
        position: "absolute",
        bottom: -8,
        left: 0,
        width: "40px",
        height: "2px",
        backgroundColor: colors.primary[500],
      },
    },
    label: {
      color: colors.blackAccent[700],
      fontWeight: 500,
      fontSize: "0.95rem",
      mb: 0.5,
      letterSpacing: 0.5,
    },
    value: {
      color: colors.blackAccent[900],
      fontWeight: 400,
      fontSize: "1.05rem",
      mb: 1.5,
    },
    divider: {
      my: 2.5,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.6,
      height: "1px",
    },
    tableHeader: {
      background: `linear-gradient(to right, ${colors.primary[400]}, ${colors.primary[500]})`,
      color: "#FFFFFF",
      fontWeight: 600,
      fontFamily: "Playfair Display, serif",
      fontSize: "1.05rem",
      padding: "12px 16px",
    },
    tableCell: {
      fontSize: "1rem",
      color: colors.blackAccent[900],
      padding: "12px 16px",
      borderBottom: `1px solid ${colors.primary[100]}`,
    },
    summaryBox: {
      background: `linear-gradient(135deg, ${colors.primary[100]}, rgba(201, 160, 99, 0.05))`,
      borderRadius: "10px",
      p: 3,
      mt: 2,
      border: `1px solid ${colors.primary[400]}`,
    },
    backBtn: {
      mb: 2,
      color: colors.primary[700],
      fontWeight: 600,
      fontFamily: "Playfair Display, serif",
      letterSpacing: 1,
      transition: "all 0.3s ease",
      "&:hover": {
        transform: "translateX(-5px)",
      },
    },
    invoiceHeader: {
      background: `linear-gradient(135deg, ${colors.primary[100]}, ${colors.primary[400]})`,
      p: 4,
      borderRadius: "12px",
      mb: 4,
      position: "relative",
      overflow: "hidden",
      boxShadow: "0px 10px 30px rgba(201, 160, 99, 0.15)",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        right: 0,
        width: "250px",
        height: "250px",
        background: `radial-gradient(circle, ${colors.primary[500]} 0%, transparent 70%)`,
        opacity: 0.15,
      },
      "&::after": {
        content: '""',
        position: "absolute",
        bottom: 0,
        left: 0,
        width: "200px",
        height: "200px",
        background: `radial-gradient(circle, ${colors.primary[500]} 0%, transparent 70%)`,
        opacity: 0.1,
      },
    },
    companyLogo: {
      width: "120px",
      height: "120px",
      objectFit: "contain",
      mb: 2,
      filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.1))",
    },
    companyName: {
      fontFamily: "Playfair Display, serif",
      fontSize: "2.5rem",
      fontWeight: 700,
      color: colors.primary[700],
      mb: 1,
      textShadow: "1px 1px 2px rgba(0, 0, 0, 0.1)",
    },
    companyTagline: {
      fontFamily: "Playfair Display, serif",
      fontSize: "1.2rem",
      color: colors.blackAccent[700],
      mb: 3,
      fontStyle: "italic",
    },
    actionButton: {
      color: colors.primary[700],
      backgroundColor: "transparent",
      border: `1px solid ${colors.primary[400]}`,
      borderRadius: "50%",
      padding: "8px",
      transition: "all 0.3s ease",
      "&:hover": {
        color: "#FFFFFF",
        backgroundColor: colors.primary[500],
        transform: "scale(1.1)",
        boxShadow: "0px 4px 10px rgba(201, 160, 99, 0.3)",
      },
    },
    totalValue: {
      fontFamily: "Playfair Display, serif",
      fontWeight: 700,
      color: colors.primary[500],
      fontSize: "1.4rem",
    },
    signatureBox: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      mt: 3,
      pt: 3,
      borderTop: `1px dashed ${colors.primary[400]}`,
    },
    signatureLine: {
      width: "200px",
      height: "1px",
      backgroundColor: colors.blackAccent[700],
      mt: 5,
      mb: 1,
    },
    signatureText: {
      fontFamily: "Playfair Display, serif",
      color: colors.blackAccent[900],
      fontWeight: 600,
      fontSize: "1rem",
    },
    footer: {
      background: `linear-gradient(135deg, ${colors.primary[100]}, rgba(201, 160, 99, 0.1))`,
      borderTop: `1px solid ${colors.primary[400]}`,
      borderRadius: "0 0 12px 12px",
      padding: "20px",
      mt: 4,
      textAlign: "center",
      position: "relative",
      overflow: "hidden",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: "50%",
        transform: "translateX(-50%)",
        width: "80%",
        height: "1px",
        background: `linear-gradient(to right, transparent, ${colors.primary[500]}, transparent)`,
      },
    },
    footerText: {
      color: colors.blackAccent[700],
      fontSize: "0.9rem",
      mb: 1,
    },
    footerHeading: {
      fontFamily: "Playfair Display, serif",
      color: colors.primary[500],
      fontWeight: 600,
      fontSize: "1.1rem",
      mb: 2,
    },
    footerIcon: {
      fontSize: "1rem",
      color: colors.primary[500],
      mr: 1,
      verticalAlign: "middle",
    },
    gstDetailBox: {
      backgroundColor: "rgba(255, 255, 255, 0.5)",
      borderRadius: "8px",
      p: 2,
      mb: 2,
      border: `1px solid ${colors.primary[400]}`,
    },
    gstLabel: {
      color: colors.blackAccent[700],
      fontWeight: 600,
      fontSize: "0.9rem",
      display: "flex",
      justifyContent: "space-between",
      mb: 1,
    },
  };

  const handlePrint = () => {
    window.print();
  };

  const handleEmail = () => {
    const subject = `Invoice #${invoice.invoiceNumber} from Star Colors Inc`;
    const body = `Dear ${invoice.customer?.name || 'Customer'},

Please find attached your invoice #${invoice.invoiceNumber} from Star Colors Inc.

Invoice Details:
- Date: ${new Date(invoice.createdAt).toLocaleDateString()}
- Total Amount: ₹${invoice.total}
- Status: ${invoice.status}

Thank you for your business!

Best regards,
Star Colors Inc`;

    const mailtoLink = `mailto:${invoice.customer?.email || ''}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;
  };

  const handleWhatsApp = () => {
    const message = `Hello ${invoice.customer?.name || 'Customer'},

Your invoice #${invoice.invoiceNumber} from Star Colors Inc is ready.

Invoice Details:
- Date: ${new Date(invoice.createdAt).toLocaleDateString()}
- Total Amount: ₹${invoice.total}
- Status: ${invoice.status}

Thank you for your business!`;

    const whatsappLink = `https://wa.me/${invoice.customer?.phone?.replace(/\D/g, '') || ''}?text=${encodeURIComponent(message)}`;
    window.open(whatsappLink, '_blank');
  };

  const handleDownload = async () => {
    try {
      setSnackbar({ open: true, message: "Generating PDF...", severity: "info" });

      const element = invoiceRef.current;
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        logging: false,
        backgroundColor: theme.palette.mode === "dark" ? "#232323" : "#fffdf8",
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
      pdf.save(`Invoice_${invoice.invoiceNumber}.pdf`);

      setSnackbar({ open: true, message: "PDF downloaded successfully!", severity: "success" });
    } catch (error) {
      console.error("Error generating PDF:", error);
      setSnackbar({ open: true, message: "Error generating PDF", severity: "error" });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (!invoice) {
    return <Typography>Invoice not found.</Typography>;
  }

  return (
    <Box m="24px">
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={4}
        sx={{
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 2, sm: 0 },
          alignItems: { xs: "flex-start", sm: "center" },
        }}
      >
        <Header
          title={`Invoice #${invoice.invoiceNumber}`}
          subtitle="View invoice details"
        />
        <Stack
          direction="row"
          spacing={2}
          sx={{
            flexWrap: { xs: "wrap", md: "nowrap" },
            justifyContent: { xs: "flex-start", sm: "flex-end" },
            gap: 1
          }}
        >
          <Tooltip title="Print Invoice" arrow placement="top">
            <IconButton onClick={handlePrint} sx={viewStyles.actionButton}>
              <PrintIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Email Invoice" arrow placement="top">
            <IconButton onClick={handleEmail} sx={viewStyles.actionButton}>
              <EmailIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Share via WhatsApp" arrow placement="top">
            <IconButton onClick={handleWhatsApp} sx={viewStyles.actionButton}>
              <WhatsAppIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Download PDF" arrow placement="top">
            <IconButton onClick={handleDownload} sx={viewStyles.actionButton}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <LuxuryButton
            onClick={() => navigate(-1)}
            startIcon={<ArrowBackIcon />}
            variant="outlined"
            sx={viewStyles.backBtn}
          >
            Back to Invoices
          </LuxuryButton>
        </Stack>
      </Box>

      <Box ref={invoiceRef}>
        <Paper sx={viewStyles.invoiceHeader} elevation={3}>
          <Grid container spacing={2}>
            {/* Company Info and Logo - Left Side */}
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <img
                  src={formatMediaUrl("/images/logo.png")}
                  alt="GD Colors Inc"
                  style={{ width: '50px', height: '50px', marginRight: '15px' }}
                />
                <Typography sx={{
                  fontFamily: "Playfair Display, serif",
                  fontSize: "1.8rem",
                  fontWeight: 700,
                  color: colors.primary[700],
                }}>
                  GD COLORS INC
                </Typography>
              </Box>
              <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                38 West 48 Street, Suite # 1004
              </Typography>
              <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                New York, NY 10036
              </Typography>
              <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                Tel: (212)391-7799 Fax: (212)391-7797
              </Typography>
              <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                Email: <EMAIL>
              </Typography>
            </Grid>

            {/* Invoice Title and Details - Right Side */}
            <Grid item xs={12} md={6}>
              <Box sx={{ textAlign: { xs: 'left', md: 'right' }, mb: 2 }}>
                <Typography variant="h4" sx={{
                  fontFamily: "Playfair Display, serif",
                  fontSize: "2rem",
                  fontWeight: 700,
                  color: colors.primary[700],
                  mb: 2
                }}>
                  Invoice
                </Typography>

                <Box sx={{
                  border: '1px solid #ccc',
                  display: 'inline-block',
                  width: { xs: '100%', md: '200px' }
                }}>
                  <Grid container>
                    <Grid item xs={6} sx={{ borderRight: '1px solid #ccc', p: 1, borderBottom: '1px solid #ccc' }}>
                      <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Date</Typography>
                    </Grid>
                    <Grid item xs={6} sx={{ p: 1, borderBottom: '1px solid #ccc' }}>
                      <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Invoice #</Typography>
                    </Grid>
                    <Grid item xs={6} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
                      <Typography sx={{ fontSize: '0.9rem' }}>
                        {invoice.createdAt
                          ? new Date(invoice.createdAt).toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric'
                            })
                          : "-"}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} sx={{ p: 1 }}>
                      <Typography sx={{ fontSize: '0.9rem' }}>{invoice.invoiceNumber}</Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Bill To and Ship To */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Box sx={{
              border: '1px solid #ccc',
              p: 2,
              height: '100%',
              borderRadius: '4px'
            }}>
              <Typography sx={{ fontWeight: 600, mb: 1 }}>Bill To</Typography>
              <Typography>{invoice.customer?.name || "N/A"}</Typography>
              <Typography>{invoice.customer?.address?.line1 || ""}</Typography>
              <Typography>{invoice.customer?.address?.line2 || ""}</Typography>
              <Typography>
                {invoice.customer?.address?.city ? `${invoice.customer.address.city}, ` : ""}
                {invoice.customer?.address?.state || ""}
                {invoice.customer?.address?.zipCode ? ` ${invoice.customer.address.zipCode}` : ""}
              </Typography>
              <Typography>{invoice.customer?.email || ""}</Typography>
              <Typography>{invoice.customer?.phone || ""}</Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{
              border: '1px solid #ccc',
              p: 2,
              height: '100%',
              borderRadius: '4px'
            }}>
              <Typography sx={{ fontWeight: 600, mb: 1 }}>Ship To</Typography>
              <Typography>{invoice.customer?.shippingAddress?.name || invoice.customer?.name || "N/A"}</Typography>
              <Typography>{invoice.customer?.shippingAddress?.line1 || invoice.customer?.address?.line1 || ""}</Typography>
              <Typography>{invoice.customer?.shippingAddress?.line2 || invoice.customer?.address?.line2 || ""}</Typography>
              <Typography>
                {invoice.customer?.shippingAddress?.city ? `${invoice.customer.shippingAddress.city}, ` :
                 invoice.customer?.address?.city ? `${invoice.customer.address.city}, ` : ""}
                {invoice.customer?.shippingAddress?.state || invoice.customer?.address?.state || ""}
                {invoice.customer?.shippingAddress?.zipCode ? ` ${invoice.customer.shippingAddress.zipCode}` :
                 invoice.customer?.address?.zipCode ? ` ${invoice.customer.address.zipCode}` : ""}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Order Info */}
        <Box sx={{
          border: '1px solid #ccc',
          mb: 3,
          borderRadius: '4px'
        }}>
          <Grid container>
            <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
              <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>P.O. Number</Typography>
            </Grid>
            <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
              <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Terms</Typography>
            </Grid>
            <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
              <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Rep</Typography>
            </Grid>
            <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
              <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Ship</Typography>
            </Grid>
            <Grid item xs={2} sx={{ p: 1 }}>
              <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Ship Via</Typography>
            </Grid>

            <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
              <Typography sx={{ fontSize: '0.9rem' }}>
                {invoice.poNumber || "SPECIAL ORDER"}
              </Typography>
            </Grid>
            <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
              <TextField
                select
                size="small"
                fullWidth
                value={invoice.terms || "Net"}
                sx={{ fontSize: '0.9rem' }}
                variant="standard"
                InputProps={{ disableUnderline: true }}
              >
                {termOptions.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
              <Typography sx={{ fontSize: '0.9rem' }}>
                {invoice.rep || "ANKUS"}
              </Typography>
            </Grid>
            <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
              <TextField
                type="date"
                size="small"
                fullWidth
                value={shippingDate ? shippingDate.toISOString().split('T')[0] : ''}
                onChange={(e) => setShippingDate(new Date(e.target.value))}
                variant="standard"
                InputProps={{ disableUnderline: true }}
              />
            </Grid>
            <Grid item xs={2} sx={{ borderTop: '1px solid #ccc', p: 1 }}>
              <TextField
                select
                size="small"
                fullWidth
                value={invoice.shipVia || "FedEx"}
                sx={{ fontSize: '0.9rem' }}
                variant="standard"
                InputProps={{ disableUnderline: true }}
              >
                {shipViaOptions.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
          </Grid>
        </Box>

        {/* Items Table */}
        <Box sx={{ mb: 3 }}>
          <TableContainer sx={{
            border: '1px solid #ccc',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{
                    fontWeight: 600,
                    backgroundColor: '#f5f5f5',
                    width: '50px'
                  }}>Lot #</TableCell>
                  <TableCell sx={{
                    fontWeight: 600,
                    backgroundColor: '#f5f5f5',
                    width: '40%'
                  }}>Description</TableCell>
                  <TableCell sx={{
                    fontWeight: 600,
                    backgroundColor: '#f5f5f5',
                    width: '80px'
                  }}>Pieces</TableCell>
                  <TableCell sx={{
                    fontWeight: 600,
                    backgroundColor: '#f5f5f5',
                    width: '100px'
                  }}>Weight/Qty</TableCell>
                  <TableCell sx={{
                    fontWeight: 600,
                    backgroundColor: '#f5f5f5',
                    width: '100px'
                  }}>Price Per Unit</TableCell>
                  <TableCell sx={{
                    fontWeight: 600,
                    backgroundColor: '#f5f5f5',
                    width: '120px'
                  }}>Amount in USD</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoice.items?.map((item, index) => (
                  <TableRow key={item._id || index} hover>
                    <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                      {index + 1}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                      {item.name}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                      {item.quantity}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                      {item.weight || '-'}
                    </TableCell>
                    <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                      ${Number(item.price).toFixed(2)}
                    </TableCell>
                    <TableCell sx={{
                      borderBottom: '1px solid #ccc',
                      fontWeight: 600
                    }}>
                      ${(item.price * item.quantity).toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
                {/* Add empty rows if needed */}
                {(!invoice.items || invoice.items.length < 5) && (
                  Array.from({ length: 5 - (invoice.items?.length || 0) }).map((_, index) => (
                    <TableRow key={`empty-${index}`}>
                      <TableCell sx={{ borderBottom: '1px solid #ccc', height: '30px' }}></TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>

        {/* Thank you message */}
        <Box sx={{
          border: '1px solid #ccc',
          p: 2,
          mb: 3,
          borderRadius: '4px',
          fontSize: '0.9rem'
        }}>
          <Typography sx={{ mb: 1 }}>Thank you for your business.</Typography>
          <Typography sx={{ mb: 1 }}>
            Terms: For resale only. Any discrepancies in date, weight & rate must be informed within 10 days.
            All overdue invoices are subject to finance charge @ 2% per month. Our liability is limited to the price of the product.
            Unless otherwise stated, all color stones have been subjected to a stable and possibly undetectable color enhancement
            process. Prevailing market values are based on those universally practiced and accepted process by the gem and jewelry trade.
            More information can be provided upon your request. Thank you for your business.
          </Typography>
        </Box>

        {/* Totals */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={7}>
            {/* Signature section can go here if needed */}
          </Grid>

          <Grid item xs={12} md={5}>
            <Box sx={{
              border: '1px solid #ccc',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <Grid container>
                <Grid item xs={8} sx={{
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  p: 1,
                  backgroundColor: '#f5f5f5',
                  fontWeight: 600
                }}>
                  Total in USD
                </Grid>
                <Grid item xs={4} sx={{
                  borderBottom: '1px solid #ccc',
                  p: 1,
                  textAlign: 'right',
                  fontWeight: 600
                }}>
                  ${invoice.total?.toFixed(2) || "0.00"}
                </Grid>

                <Grid item xs={8} sx={{
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  p: 1,
                  backgroundColor: '#f5f5f5',
                  fontWeight: 600
                }}>
                  Payments/Credits
                </Grid>
                <Grid item xs={4} sx={{
                  borderBottom: '1px solid #ccc',
                  p: 1,
                  textAlign: 'right'
                }}>
                  $0.00
                </Grid>

                <Grid item xs={8} sx={{
                  borderRight: '1px solid #ccc',
                  p: 1,
                  backgroundColor: '#f5f5f5',
                  fontWeight: 600
                }}>
                  Balance Due
                </Grid>
                <Grid item xs={4} sx={{
                  p: 1,
                  textAlign: 'right',
                  fontWeight: 600
                }}>
                  ${invoice.total?.toFixed(2) || "0.00"}
                </Grid>
              </Grid>
            </Box>
          </Grid>
        </Grid>

      </Box>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            width: "100%",
            border: `1px solid ${colors.primary[400]}`,
            boxShadow: "0px 4px 12px rgba(201, 160, 99, 0.15)",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ViewInvoice;
