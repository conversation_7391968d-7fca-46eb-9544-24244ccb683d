import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  useTheme,
  Divider
} from '@mui/material';
import LuxuryButton from '../components/LuxuryButton';
import { tokens } from '../theme';
import Header from '../components/Header';
import JewelryStatBox from '../components/JewelryStatBox';
import JewelryCard from '../components/JewelryCard';

// Icons
import DiamondIcon from '@mui/icons-material/Diamond';
import ShoppingBagIcon from '@mui/icons-material/ShoppingBag';
import PersonIcon from '@mui/icons-material/Person';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import InventoryIcon from '@mui/icons-material/Inventory';
import AddIcon from '@mui/icons-material/Add';

// Sample data
const sampleInventoryItems = [
  {
    _id: '1',
    name: 'Diamond Engagement Ring',
    category: 'Rings',
    description: 'Beautiful 1.5 carat diamond engagement ring with platinum band',
    sku: 'RNG-DM-1001',
    weight: { netWt: { value: 5.2, unit: 'g' } },
    purity: '950 Platinum',
    stock: { quantity: 3 },
    price: { sellingPrice: 125000 },
    images: ['https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80']
  },
  {
    _id: '2',
    name: 'Gold Chain Necklace',
    category: 'Necklaces',
    description: '22K Gold chain necklace with intricate design',
    sku: 'NCK-GD-2002',
    weight: { netWt: { value: 12.8, unit: 'g' } },
    purity: '22K Gold',
    stock: { quantity: 5 },
    price: { sellingPrice: 78500 },
    images: ['https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80']
  },
  {
    _id: '3',
    name: 'Pearl Earrings',
    category: 'Earrings',
    description: 'Elegant pearl earrings with white gold settings',
    sku: 'EAR-PL-3003',
    weight: { netWt: { value: 3.5, unit: 'g' } },
    purity: '18K White Gold',
    stock: { quantity: 8 },
    price: { sellingPrice: 45000 },
    images: ['https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80']
  },
  {
    _id: '4',
    name: 'Ruby Bracelet',
    category: 'Bracelets',
    description: 'Stunning ruby bracelet with diamond accents',
    sku: 'BRC-RB-4004',
    weight: { netWt: { value: 15.3, unit: 'g' } },
    purity: '18K Gold',
    stock: { quantity: 2 },
    price: { sellingPrice: 95000 },
    images: ['https://images.unsplash.com/photo-1611652022419-a9419f74343d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80']
  }
];

const JewelryDashboard = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const handleViewItem = (id) => {
    console.log('View item:', id);
  };

  const handleEditItem = (id) => {
    console.log('Edit item:', id);
  };

  const handleDeleteItem = (id) => {
    console.log('Delete item:', id);
  };

  return (
    <Box m={{ xs: "10px", sm: "15px", md: "20px" }} maxWidth="100%" overflow="hidden">
      {/* HEADER */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "space-between",
          alignItems: { xs: "flex-start", sm: "center" },
          gap: { xs: 2, sm: 0 }
        }}
      >
        <Header title="JEWELRY DASHBOARD" subtitle="Welcome to your jewelry business management system" />

        <LuxuryButton
          startIcon={<AddIcon />}
          sx={{ alignSelf: { xs: "flex-start", sm: "auto" } }}
        >
          Add New Item
        </LuxuryButton>
      </Box>

      {/* STATS GRID */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: { xs: 2, sm: 1 }, mb: { xs: 3, sm: 4 } }}>
        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Total Sales"
            value="₹1,245,300"
            subtitle="Last 30 days"
            icon={<AttachMoneyIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="12%"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Orders"
            value="48"
            subtitle="Last 30 days"
            icon={<ShoppingBagIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="8%"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Customers"
            value="156"
            subtitle="Total active customers"
            icon={<PersonIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="5%"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Inventory Value"
            value="₹4,850,000"
            subtitle="Total inventory value"
            icon={<DiamondIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="15%"
          />
        </Grid>
      </Grid>

      {/* INVENTORY OVERVIEW */}
      <Box mb={{ xs: 3, sm: 4 }}>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            justifyContent: "space-between",
            alignItems: { xs: "flex-start", sm: "center" },
            gap: { xs: 2, sm: 0 },
            mb: { xs: 2, sm: 3 }
          }}
        >
          <Typography
            variant="h3"
            sx={{
              color: colors.primary[500],
              fontFamily: '"Playfair Display", serif',
              fontWeight: 500,
              position: 'relative',
              display: 'inline-block',
              paddingBottom: '8px',
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
              '&::after': {
                content: '""',
                position: 'absolute',
                width: '40%',
                height: '2px',
                bottom: 0,
                left: 0,
                backgroundColor: colors.primary[500],
              }
            }}
          >
            Featured Inventory
          </Typography>

          <LuxuryButton
            variant="outlined"
            endIcon={<InventoryIcon />}
            sx={{ alignSelf: { xs: "flex-start", sm: "auto" } }}
          >
            View All Inventory
          </LuxuryButton>
        </Box>

        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {sampleInventoryItems.map((item) => (
            <Grid item xs={12} sm={6} lg={3} key={item._id}>
              <JewelryCard
                item={item}
                onView={handleViewItem}
                onEdit={handleEditItem}
                onDelete={handleDeleteItem}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* SALES ANALYTICS */}
      <Box mb={{ xs: 3, sm: 4 }}>
        <Typography
          variant="h3"
          sx={{
            color: colors.primary[500],
            fontFamily: '"Playfair Display", serif',
            fontWeight: 500,
            mb: { xs: 2, sm: 3 },
            position: 'relative',
            display: 'inline-block',
            paddingBottom: '8px',
            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
            '&::after': {
              content: '""',
              position: 'absolute',
              width: '40%',
              height: '2px',
              bottom: 0,
              left: 0,
              backgroundColor: colors.primary[500],
            }
          }}
        >
          Sales Analytics
        </Typography>

        <Grid container spacing={{ xs: 2, sm: 3 }}>
          <Grid item xs={12} md={8}>
            <Card
              sx={{
                borderRadius: '12px',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                height: '100%',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: { xs: 'none', sm: 'translateY(-5px)' },
                  boxShadow: { xs: '0 4px 20px rgba(0, 0, 0, 0.08)', sm: '0 8px 30px rgba(0, 0, 0, 0.12)' },
                }
              }}
            >
              <CardContent>
                <Typography
                  variant="h4"
                  sx={{
                    color: colors.primary[500],
                    fontFamily: '"Playfair Display", serif',
                    mb: 2,
                    fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.5rem' }
                  }}
                >
                  Monthly Sales Trend
                </Typography>

                <Box
                  sx={{
                    height: { xs: '200px', sm: '250px', md: '300px' },
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: colors.whiteAccent[200],
                    borderRadius: '8px'
                  }}
                >
                  <Typography variant="body1" color={colors.blackAccent[600]}>
                    Sales chart will be displayed here
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card
              sx={{
                borderRadius: '12px',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                height: '100%',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: { xs: 'none', sm: 'translateY(-5px)' },
                  boxShadow: { xs: '0 4px 20px rgba(0, 0, 0, 0.08)', sm: '0 8px 30px rgba(0, 0, 0, 0.12)' },
                }
              }}
            >
              <CardContent>
                <Typography
                  variant="h4"
                  sx={{
                    color: colors.primary[500],
                    fontFamily: '"Playfair Display", serif',
                    mb: 2,
                    fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.5rem' }
                  }}
                >
                  Top Categories
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: colors.blackAccent[700] }}>
                      Rings
                    </Typography>
                    <Typography variant="body1" sx={{ color: colors.primary[500], fontWeight: 'bold' }}>
                      35%
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      width: '100%',
                      height: '8px',
                      backgroundColor: colors.whiteAccent[300],
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        width: '35%',
                        height: '100%',
                        backgroundColor: colors.primary[500],
                        borderRadius: '4px'
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: colors.blackAccent[700] }}>
                      Necklaces
                    </Typography>
                    <Typography variant="body1" sx={{ color: colors.primary[500], fontWeight: 'bold' }}>
                      28%
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      width: '100%',
                      height: '8px',
                      backgroundColor: colors.whiteAccent[300],
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        width: '28%',
                        height: '100%',
                        backgroundColor: colors.primary[500],
                        borderRadius: '4px'
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: colors.blackAccent[700] }}>
                      Earrings
                    </Typography>
                    <Typography variant="body1" sx={{ color: colors.primary[500], fontWeight: 'bold' }}>
                      20%
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      width: '100%',
                      height: '8px',
                      backgroundColor: colors.whiteAccent[300],
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        width: '20%',
                        height: '100%',
                        backgroundColor: colors.primary[500],
                        borderRadius: '4px'
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: colors.blackAccent[700] }}>
                      Bracelets
                    </Typography>
                    <Typography variant="body1" sx={{ color: colors.primary[500], fontWeight: 'bold' }}>
                      17%
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      width: '100%',
                      height: '8px',
                      backgroundColor: colors.whiteAccent[300],
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        width: '17%',
                        height: '100%',
                        backgroundColor: colors.primary[500],
                        borderRadius: '4px'
                      }}
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* RECENT ORDERS */}
      <Box>
        <Typography
          variant="h3"
          sx={{
            color: colors.primary[500],
            fontFamily: '"Playfair Display", serif',
            fontWeight: 500,
            mb: { xs: 2, sm: 3 },
            position: 'relative',
            display: 'inline-block',
            paddingBottom: '8px',
            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
            '&::after': {
              content: '""',
              position: 'absolute',
              width: '40%',
              height: '2px',
              bottom: 0,
              left: 0,
              backgroundColor: colors.primary[500],
            }
          }}
        >
          Recent Orders
        </Typography>

        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            '&:hover': {
              transform: { xs: 'none', sm: 'translateY(-5px)' },
              boxShadow: { xs: '0 4px 20px rgba(0, 0, 0, 0.08)', sm: '0 8px 30px rgba(0, 0, 0, 0.12)' },
            }
          }}
        >
          <CardContent>
            <Box sx={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse', minWidth: '650px' }}>
                <thead>
                  <tr>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700],
                      fontFamily: '"Inter", sans-serif',
                      fontWeight: 600,
                      whiteSpace: 'nowrap'
                    }}>
                      Order ID
                    </th>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700],
                      fontFamily: '"Inter", sans-serif',
                      fontWeight: 600,
                      whiteSpace: 'nowrap'
                    }}>
                      Customer
                    </th>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700],
                      fontFamily: '"Inter", sans-serif',
                      fontWeight: 600,
                      whiteSpace: 'nowrap'
                    }}>
                      Items
                    </th>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700],
                      fontFamily: '"Inter", sans-serif',
                      fontWeight: 600,
                      whiteSpace: 'nowrap'
                    }}>
                      Amount
                    </th>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700],
                      fontFamily: '"Inter", sans-serif',
                      fontWeight: 600,
                      whiteSpace: 'nowrap'
                    }}>
                      Status
                    </th>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700],
                      fontFamily: '"Inter", sans-serif',
                      fontWeight: 600,
                      whiteSpace: 'nowrap'
                    }}>
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      #ORD-5289
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      Priya Sharma
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      Diamond Engagement Ring
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.primary[500],
                      fontWeight: 600
                    }}>
                      ₹125,000
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                    }}>
                      <Box sx={{
                        backgroundColor: colors.greenAccent[100],
                        color: colors.greenAccent[600],
                        borderRadius: '4px',
                        padding: '4px 8px',
                        display: 'inline-block',
                        fontWeight: 500,
                        fontSize: '0.875rem'
                      }}>
                        Completed
                      </Box>
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[600]
                    }}>
                      May 15, 2023
                    </td>
                  </tr>
                  <tr>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      #ORD-5288
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      Rahul Patel
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      Gold Chain Necklace
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.primary[500],
                      fontWeight: 600
                    }}>
                      ₹78,500
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                    }}>
                      <Box sx={{
                        backgroundColor: colors.primary[100],
                        color: colors.primary[700],
                        borderRadius: '4px',
                        padding: '4px 8px',
                        display: 'inline-block',
                        fontWeight: 500,
                        fontSize: '0.875rem'
                      }}>
                        Processing
                      </Box>
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[600]
                    }}>
                      May 14, 2023
                    </td>
                  </tr>
                  <tr>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      #ORD-5287
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      Ananya Gupta
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[700]
                    }}>
                      Pearl Earrings
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.primary[500],
                      fontWeight: 600
                    }}>
                      ₹45,000
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                    }}>
                      <Box sx={{
                        backgroundColor: colors.greenAccent[100],
                        color: colors.greenAccent[600],
                        borderRadius: '4px',
                        padding: '4px 8px',
                        display: 'inline-block',
                        fontWeight: 500,
                        fontSize: '0.875rem'
                      }}>
                        Completed
                      </Box>
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      borderBottom: `1px solid ${colors.blackAccent[200]}`,
                      color: colors.blackAccent[600]
                    }}>
                      May 12, 2023
                    </td>
                  </tr>
                </tbody>
              </table>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: { xs: 2, sm: 3 } }}>
              <LuxuryButton
                variant="outlined"
                endIcon={<TrendingUpIcon />}
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              >
                View All Orders
              </LuxuryButton>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default JewelryDashboard;
