const { Joi, common } = require('../../util/validations');
const { OtpType } = require('../../../lib/models/enums');

const requestOtp = Joi.object().keys({
    type: Joi.string()
        .valid(...Object.values(OtpType))
        .required()
        .messages({
            'any.required': 'OTP type is required',
            'any.only': 'Invalid OTP type'
        }),
    countryCode: common.countryCode,
    phone: common.phone
});

const verifyOtp = requestOtp.keys({
    token: common.otp
});

const signUp = Joi.object().keys({
    fullName: Joi.string()
        .trim()
        .min(3)
        .max(30)
        .required()
        .messages({
            'string.min': 'Name must be at least 3 characters long',
            'string.max': 'Name cannot exceed 30 characters',
            'any.required': 'Name is required'
        }),
    age: common.age,
    email: common.email.optional(),
    countryCode: common.countryCode,
    phone: common.phone,
    password: common.password,
    deviceToken: Joi.string()
        .trim()
        .optional()
});

const logIn = Joi.object().keys({
    countryCode: common.countryCode,
    phone: common.phone,
    password: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Password is required'
        }),
    deviceToken: Joi.string()
        .trim()
        .optional()
});

const resetPassword = Joi.object().keys({
    countryCode: common.countryCode,
    phone: common.phone,
    password: common.password
});

module.exports = {
    requestOtp,
    verifyOtp,
    signUp,
    logIn,
    resetPassword
};
