import {
  Box,
  Card,
  CardContent,
  Divider,
  Grid,
  Typography,
  useTheme,
} from "@mui/material";
import { JewelryFormField, LuxuryButton } from "components";
import Header from "components/Header";
import {
  customerInitialValues,
  customerValidationSchema,
} from "constants/schemas/customerSchema";
import { useFormik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { tokens } from "theme";
import { useAddCustomerMutation, useGetCustomerInfoQuery, useUpdateCustomerMutation } from "services";
import { preFillValues } from "utils/common";

const Add = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const navigate = useNavigate();
  const { id: customerId } = useParams();

  const { data: customerData } = useGetCustomerInfoQuery(customerId, {
    skip: !customerId,
    refetchOnMountOrArgChange: true,
  });

  // mutations
  const [addCustomer] = useAddCustomerMutation();
  const [updateCustomer] = useUpdateCustomerMutation();

  const formik = useFormik({
    initialValues: preFillValues(customerInitialValues, customerData),
    enableReinitialize: true,
    validationSchema: customerValidationSchema,
    onSubmit: (values) => {
      if (customerId) {
        updateCustomer({
          customerId,
          payload: values,
        })
          .unwrap()
          .then((result) => {
            if (result?._id) {
              toast.success("Customer updated successfully");
              navigate("/customers");
            }
          });
      } else {
        addCustomer(values)
          .unwrap()
          .then((result) => {
            if (result?.data?._id) {
              toast.success("Customer saved successfully");
              formik.resetForm();
              navigate("/customers");
            }
          });
      }
    },
  });

  const getError = (name) => formik.touched[name] && formik.errors[name];

  // Custom styles for the form sections
  const sectionStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.08)",
      overflow: "hidden",
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      height: "100%",
      mb: 3,
      "&:hover": {
        transform: "translateY(-5px)",
        boxShadow: "0px 8px 30px rgba(0, 0, 0, 0.12)",
      },
    },
    sectionTitle: {
      fontFamily: '"Playfair Display", serif',
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: "relative",
      display: "inline-block",
      paddingBottom: "8px",
      "&::after": {
        content: '""',
        position: "absolute",
        width: "40%",
        height: "2px",
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
  };

  return (
    <Box m="20px">
      <Header
        title={`${customerId ? "EDIT" : "CREATE"} CUSTOMER`}
        subtitle={`${customerId ? "Edit Customer" : "Create a New Customer"}`}
      />

      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          {/* BASIC INFORMATION */}
          <Grid item xs={12}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Basic Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <JewelryFormField
                      label="Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={!!getError("name")}
                      helperText={getError("name")}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <JewelryFormField
                      type="email"
                      label="Email"
                      name="email"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={!!getError("email")}
                      helperText={getError("email")}
                      required
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* CONTACT INFORMATION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Contact Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box sx={{ display: "grid", gap: "20px" }}>
                  <JewelryFormField
                    label="Phone"
                    name="phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("phone")}
                    helperText={getError("phone")}
                    required
                  />
                  <JewelryFormField
                    label="Alternate Phone"
                    name="alternatePhone"
                    value={formik.values.alternatePhone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("alternatePhone")}
                    helperText={getError("alternatePhone")}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* ADDRESS INFORMATION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Address Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box sx={{ display: "grid", gap: "20px" }}>
                  <JewelryFormField
                    label="Street"
                    name="address.street"
                    value={formik.values.address.street}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("address.street")}
                    helperText={getError("address.street")}
                    required
                  />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <JewelryFormField
                        label="City"
                        name="address.city"
                        value={formik.values.address.city}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={!!getError("address.city")}
                        helperText={getError("address.city")}
                        required
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <JewelryFormField
                        label="State"
                        name="address.state"
                        value={formik.values.address.state}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={!!getError("address.state")}
                        helperText={getError("address.state")}
                        required
                      />
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <JewelryFormField
                        label="Postal Code"
                        name="address.postalCode"
                        value={formik.values.address.postalCode}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={!!getError("address.postalCode")}
                        helperText={getError("address.postalCode")}
                        required
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <JewelryFormField
                        label="Country"
                        name="address.country"
                        value={formik.values.address.country}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={!!getError("address.country")}
                        helperText={getError("address.country")}
                        required
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* BUSINESS INFORMATION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Business Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box sx={{ display: "grid", gap: "20px" }}>
                  <JewelryFormField
                    label="GST Number"
                    name="gstNumber"
                    value={formik.values.gstNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("gstNumber")}
                    helperText={getError("gstNumber")}
                  />
                  <JewelryFormField
                    label="PAN Card Number"
                    name="panCardNumber"
                    value={formik.values.panCardNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("panCardNumber")}
                    helperText={getError("panCardNumber")}
                  />
                  <JewelryFormField
                    label="Aadhar Number"
                    name="aadharNumber"
                    value={formik.values.aadharNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("aadharNumber")}
                    helperText={getError("aadharNumber")}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* PERSONAL INFORMATION */}
          <Grid item xs={12} md={6}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Personal Information
                </Typography>
                <Divider sx={sectionStyles.divider} />

                <Box sx={{ display: "grid", gap: "20px" }}>
                  <JewelryFormField
                    type="date"
                    label="Date of Birth"
                    name="dateOfBirth"
                    value={formik.values.dateOfBirth}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("dateOfBirth")}
                    helperText={getError("dateOfBirth")}
                    InputLabelProps={{ shrink: true }}
                  />
                  <JewelryFormField
                    type="date"
                    label="Anniversary"
                    name="anniversary"
                    value={formik.values.anniversary}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("anniversary")}
                    helperText={getError("anniversary")}
                    InputLabelProps={{ shrink: true }}
                  />
                  <JewelryFormField
                    type="select"
                    label="Customer Type"
                    name="customerType"
                    value={formik.values.customerType}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={!!getError("customerType")}
                    helperText={getError("customerType")}
                    options={[
                      { value: "retail", label: "Retail" },
                      { value: "other", label: "Other" },
                    ]}
                    required
                  />
                  <JewelryFormField
                    label="Tags (comma separated)"
                    name="tags"
                    value={formik.values.tags.join(", ")}
                    onChange={(e) =>
                      formik.setFieldValue(
                        "tags",
                        e.target.value
                          .split(",")
                          .map((t) => t.trim())
                          .filter(Boolean)
                      )
                    }
                    onBlur={formik.handleBlur}
                    error={!!getError("tags")}
                    helperText={getError("tags")}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* SUBMIT BUTTON */}
        <Box display="flex" justifyContent="flex-end" mt="30px">
          <LuxuryButton type="submit" size="large">
            {customerId ? "Update Customer" : "Create Customer"}
          </LuxuryButton>
        </Box>
      </form>
    </Box>
  );
};

export default Add;
