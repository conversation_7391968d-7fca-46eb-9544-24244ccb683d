import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  Box,
  useTheme,
  Divider
} from '@mui/material';
import { tokens } from '../theme';

/**
 * Dialog component for selecting a size before printing a label
 * 
 * @param {Object} props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when the dialog is closed
 * @param {Array} props.sizes - Array of size objects with value and quantity
 * @param {Function} props.onSizeSelect - Function to call when a size is selected
 * @param {string} props.sku - The SKU of the item
 * @param {string} props.name - The name of the item
 */
const SizeSelectionDialog = ({ open, onClose, sizes, onSizeSelect, sku, name }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  // If there are no sizes or only one size, we don't need to show the dialog
  if (!sizes || sizes.length === 0) {
    return null;
  }

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.12)',
        }
      }}
    >
      <DialogTitle sx={{ 
        fontFamily: '"Playfair Display", serif',
        color: colors.primary[500],
        borderBottom: `1px solid ${colors.primary[200]}`,
        pb: 2
      }}>
        Select Size for Label
      </DialogTitle>
      
      <DialogContent sx={{ mt: 2 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            {name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            SKU: {sku}
          </Typography>
        </Box>
        
        <Typography variant="body2" sx={{ mb: 1, color: colors.blackAccent[700] }}>
          Please select a size to print on the label:
        </Typography>
        
        <List sx={{ 
          border: `1px solid ${colors.primary[200]}`,
          borderRadius: '8px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {sizes.map((size, index) => (
            <React.Fragment key={index}>
              {index > 0 && <Divider />}
              <ListItem disablePadding>
                <ListItemButton 
                  onClick={() => onSizeSelect(size.value)}
                  sx={{
                    '&:hover': {
                      backgroundColor: colors.primary[100],
                    }
                  }}
                >
                  <ListItemText 
                    primary={
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {size.value}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        Quantity: {size.quantity}
                      </Typography>
                    }
                  />
                </ListItemButton>
              </ListItem>
            </React.Fragment>
          ))}
        </List>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button 
          onClick={onClose}
          sx={{
            color: colors.blackAccent[700],
            '&:hover': {
              backgroundColor: colors.blackAccent[100],
            }
          }}
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SizeSelectionDialog;
