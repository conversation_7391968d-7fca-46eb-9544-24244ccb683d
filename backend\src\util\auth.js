const jwt = require('jsonwebtoken');
const { getPlatform } = require('./common');
const { Admin, User } = require('../../lib/models/models');

const signToken = (payload, platform) => {
    try {
        // Debug environment
        console.log('Environment:', {
            nodeEnv: process.env.NODE_ENV,
            jwtSecretExists: !!process.env.JWT_SECRET,
        });

        // Ensure JWT_SECRET is available
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is not configured in .env.api file');
        }

        // Ensure all values are strings
        const jwtPayload = {
            sub: payload.id ? String(payload.id) : '',
            role: payload.role ? String(payload.role) : '',
            aud: platform ? String(platform) : ''
        };

        console.log('Signing token with payload:', jwtPayload);

        return jwt.sign(
            jwtPayload,
            secret,
            { expiresIn: '30d' }
        );
    } catch (error) {
        console.error('Token signing error:', error);
        throw error; // Throw the original error for better debugging
    }
};


const verifyToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // ✅ Correctly extract the token

    if (!token) {
        return res.unauthorized('', req.__('UNAUTHORIZED'));
    }

    jwt.verify(token, process.env.JWT_SECRET, async (err, decoded) => {
        const platform = getPlatform(req);

        if (err || !decoded || !decoded.sub || decoded.aud !== platform) {
            return res.unauthorized('', req.__('UNAUTHORIZED'));
        }

        let user = await User.findOne({
            _id: decoded.sub,
            isDeleted: false,
            authTokenIssuedAt: decoded.iat,
        }).lean();

        if (!user) {
            user = await Admin.findOne({
                _id: decoded.sub,
                isDeleted: false,
                authTokenIssuedAt: decoded.iat,
            }).lean();
        }

        if (!user) {
            return res.unauthorized('', req.__('UNAUTHORIZED'));
        }

        if (user.isSuspended) {
            return res.unauthorized('', req.__('YOUR_ACCOUNT_SUSPENDED'));
        }

        req.user = user;
        res.user = user;
        next();
    });
};

const verifyTokenAdmin = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // <--- fix here

        if (!token) {
            return res.status(401).json({ message: req.__('UNAUTHORIZED') });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const platform = getPlatform(req);

        if (!decoded || decoded.aud !== platform) {
            return res.status(401).json({ message: req.__('UNAUTHORIZED') });
        }

        const user = await Admin.findOne({
            _id: decoded.sub,
            isDeleted: false,
            authTokenIssuedAt: decoded.iat,
        }).lean();

        req.user = user;

        if (!req.user || req.user.isSuspended) {
            return res.status(401).json({ message: req.__('UNAUTHORIZED') });
        }

        next();
    } catch (error) {
        console.error('Error in verifyTokenAdmin:', error);
        return res.status(500).json({ message: req.__('INTERNAL_SERVER_ERROR') });
    }
};











const verifyTokenOptional = (req, res, next) => {
    if (!req.headers.authorization) {
        req.user = null;
        res.user = null;
        next();
    } else {
        jwt.verify(req.headers.authorization, process.env.JWT_SECRET, async (err, decoded) => {
            const platform = getPlatform(req);
            if (err || !decoded || !decoded.sub || decoded.aud !== platform) {
                return res.unauthorized('', req.__('UNAUTHORIZED'));
            }

            let user = await User.findOne({
                _id: decoded.sub,
                isDeleted: false,
                authTokenIssuedAt: decoded.iat,
            });

            if (!user) {
                user = await Admin.findOne({
                    _id: decoded.sub,
                    isDeleted: false,
                    authTokenIssuedAt: decoded.iat,
                });
            }

            if (!user) {
                return res.unauthorized('', req.__('UNAUTHORIZED'));
            }

            if (user.isSuspended) {
                return res.unauthorized('', req.__('YOUR_ACCOUNT_SUSPENDED'));
            }

            req.user = user;
            res.user = user;
            next();
        });
    }
};

const verifyTokenSocket = (token, language = 'en') =>
    jwt.verify(token, process.env.JWT_SECRET, async (err, decoded) => {
        const __ = withLanguage(language);

        if (err || !decoded || !decoded.sub) {
            return {
                error: true,
                msg: __('UNAUTHORIZED'),
            };
        }

        const user = await User.findOne({
            _id: decoded.sub,
            isDeleted: false,
            authTokenIssuedAt: decoded.iat,
        });

        if (!user) {
            return {
                error: true,
                msg: __('UNAUTHORIZED'),
            };
        }

        if (user.isSuspended) {
            return {
                error: true,
                msg: __('YOUR_ACCOUNT_SUSPENDED'),
            };
        }

        return {
            error: false,
            data: {
                user,
            },
        };
    });

const verifyTokenUserOrAdmin = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // <--- fix here
    if (!token) {
        return res.status(401).json({ message: req.__('UNAUTHORIZED') });
    }
    const decodedToken = jwt.decode(token);
    try {
        if (decodedToken && decodedToken.user_id != undefined) {
            req.user = await User.findOne({ fcmId: decodedToken.user_id });
            if (!req.user || req.user.isSuspended) {
                return res.status(401).json({ message: req.__('UNAUTHORIZED') });
            }

            next();
        } else {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const platform = getPlatform(req);

            if (!decoded || decoded.aud !== platform) {
                return res.status(401).json({ message: req.__('UNAUTHORIZED') });
            }

            const user = await Admin.findOne({
                _id: decoded.sub,
                isDeleted: false,
                authTokenIssuedAt: decoded.iat,
            });

            if (!user) {
                req.user = await User.findOne({
                    _id: decoded.sub,
                    isDeleted: false,
                    authTokenIssuedAt: decoded.iat,
                });
            } else {
                req.user = user;
            }

            if (!req.user || req.user.isSuspended) {
                return res.status(401).json({ message: req.__('UNAUTHORIZED') });
            }

            next();
        }
    } catch (error) {
        console.error('Error in verifyTokenUserOrAdmin:', error);
        return res.status(500).json({ message: req.__('INTERNAL_SERVER_ERROR') });
    }
};



module.exports = {
    signToken,
    verifyToken,
    verifyTokenOptional,
    verifyTokenSocket,
    verifyTokenUserOrAdmin,
    verifyTokenAdmin,
};
