import React, { useState, useEffect } from 'react';
import { Box, Grid, Card, Typography, Divider, useTheme, Button, CircularProgress, Chip, IconButton, Tooltip } from '@mui/material';
import { tokens } from 'theme';
import Header from 'components/Header';
import JewelryStatBox from 'components/JewelryStatBox';
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip } from 'recharts';
import { DataGrid } from '@mui/x-data-grid';
import { LuxuryButton } from 'components';

// Icons
import DiamondIcon from '@mui/icons-material/Diamond';
import InventoryIcon from '@mui/icons-material/Inventory';
import ShoppingBagIcon from '@mui/icons-material/ShoppingBag';
import PersonIcon from '@mui/icons-material/Person';
import WarningIcon from '@mui/icons-material/Warning';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PrintIcon from '@mui/icons-material/Print';
import AddIcon from '@mui/icons-material/Add';
import RefreshIcon from '@mui/icons-material/Refresh';

// Services
import {
  useGetDashboardInfoQuery,
  useGetInventoryStatsQuery,
  useGetRecentInventoryQuery,
  useGetLowStockItemsQuery,
  useGetRecentInvoicesQuery,
  useGetCategoryDistributionQuery
} from 'services/dashboard.service';
import { useGetInventoriesQuery } from 'services';
import { formatCurrency, formatTimestamp } from 'utils/common';
import { Link, useNavigate } from 'react-router-dom';

const JewelryDashboard = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const navigate = useNavigate();

  // Queries
  const { data: dashboardInfo, isLoading: isLoadingDashboard, refetch: refetchDashboard } = useGetDashboardInfoQuery();
  const { data: inventoryStats, isLoading: isLoadingStats, refetch: refetchStats } = useGetInventoriesQuery({
    params: { limit: 1 } // Just to get the pagination info
  });
  const { data: recentInventory, isLoading: isLoadingRecent, refetch: refetchRecent } = useGetRecentInventoryQuery();
  const { data: lowStockItems, isLoading: isLoadingLowStock, refetch: refetchLowStock } = useGetLowStockItemsQuery();
  const { data: recentInvoices, isLoading: isLoadingInvoices, refetch: refetchInvoices } = useGetRecentInvoicesQuery();

  // Derived data
  const totalInventoryItems = inventoryStats?.pagination?.total || 0;
  const totalInventoryValue = calculateTotalInventoryValue(recentInventory?.items || []);
  const lowStockCount = lowStockItems?.items?.length || 0;
  const totalCustomers = dashboardInfo?.usersCount || 0;
  const totalSales = dashboardInfo?.todaysCollection || 0;

  // Category distribution for pie chart
  const categoryData = prepareCategoryData(recentInventory?.items || []);

  // Refresh all data
  const refreshAllData = () => {
    refetchDashboard();
    refetchStats();
    refetchRecent();
    refetchLowStock();
    refetchInvoices();
  };

  // Calculate total inventory value
  function calculateTotalInventoryValue(items) {
    return items.reduce((total, item) => {
      const price = parseFloat(item.price?.sellingPrice) || 0;
      const quantity = item.stock?.quantity || 0;
      return total + (price * quantity);
    }, 0);
  }

  // Prepare category data for pie chart
  function prepareCategoryData(items) {
    const categories = {};

    items.forEach(item => {
      const category = item.category || 'Other';
      if (!categories[category]) {
        categories[category] = 0;
      }
      categories[category]++;
    });

    return Object.keys(categories).map(key => ({
      name: key.charAt(0).toUpperCase() + key.slice(1),
      value: categories[key]
    }));
  }

  // Recent inventory columns
  const recentInventoryColumns = [
    { field: 'sku', headerName: 'SKU', flex: 1 },
    { field: 'name', headerName: 'Name', flex: 1.5 },
    { field: 'category', headerName: 'Category', flex: 1,
      renderCell: (params) => (
        <Chip
          label={params.value?.charAt(0).toUpperCase() + params.value?.slice(1) || 'N/A'}
          size="small"
          sx={{
            backgroundColor: colors.primary[100],
            color: colors.primary[700]
          }}
        />
      )
    },
    { field: 'stock', headerName: 'Stock', flex: 0.7,
      valueGetter: (params) => params.row.stock?.quantity || 0
    },
    { field: 'actions', headerName: 'Actions', flex: 1, sortable: false,
      renderCell: (params) => (
        <Box>
          <Tooltip title="View Details">
            <IconButton
              component={Link}
              to={`/inventory/${params.row._id}`}
              size="small"
              sx={{
                color: colors.blackAccent[700],
                '&:hover': { color: colors.primary[500] }
              }}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  // Low stock columns
  const lowStockColumns = [
    { field: 'sku', headerName: 'SKU', flex: 1 },
    { field: 'name', headerName: 'Name', flex: 1.5 },
    { field: 'stock', headerName: 'Stock', flex: 0.7,
      valueGetter: (params) => params.row.stock?.quantity || 0,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography
            sx={{
              color: (params.row.stock?.quantity <= params.row.stock?.minStockLevel)
                ? colors.redAccent[500]
                : colors.grey[100]
            }}
          >
            {params.row.stock?.quantity || 0}
          </Typography>
          {params.row.stock?.quantity <= params.row.stock?.minStockLevel && (
            <WarningIcon
              sx={{
                color: colors.redAccent[500],
                fontSize: '16px',
                ml: 0.5
              }}
            />
          )}
        </Box>
      )
    },
    { field: 'minLevel', headerName: 'Min Level', flex: 0.7,
      valueGetter: (params) => params.row.stock?.minStockLevel || 0
    },
    { field: 'actions', headerName: 'Actions', flex: 1, sortable: false,
      renderCell: (params) => (
        <Box>
          <Tooltip title="View Details">
            <IconButton
              component={Link}
              to={`/inventory/${params.row._id}`}
              size="small"
              sx={{
                color: colors.blackAccent[700],
                '&:hover': { color: colors.primary[500] }
              }}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  // COLORS FOR PIE CHART
  const COLORS = [
    colors.primary[500],
    colors.greenAccent[500],
    colors.blueAccent[500],
    colors.redAccent[500],
    colors.primary[300],
    colors.greenAccent[300],
    colors.blueAccent[300],
    colors.redAccent[300],
  ];

  return (
    <Box m={{ xs: "10px", sm: "15px", md: "20px" }} maxWidth="100%" overflow="hidden">
      {/* HEADER */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "space-between",
          alignItems: { xs: "flex-start", sm: "center" },
          gap: { xs: 2, sm: 0 }
        }}
      >
        <Header title="JEWELRY DASHBOARD" subtitle="Welcome to your jewelry business management system" />

        <Box display="flex" gap={2}>
          <LuxuryButton
            startIcon={<RefreshIcon />}
            variant="outlined"
            onClick={refreshAllData}
          >
            Refresh
          </LuxuryButton>
          <LuxuryButton
            startIcon={<AddIcon />}
            onClick={() => navigate("/inventory/add")}
          >
            Add New Item
          </LuxuryButton>
        </Box>
      </Box>

      {/* STATS GRID */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: { xs: 2, sm: 1 }, mb: { xs: 3, sm: 4 } }}>
        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Inventory Value"
            value={`$${formatCurrency(totalInventoryValue)}`}
            subtitle={`${totalInventoryItems} total items`}
            icon={<DiamondIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="15%"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Low Stock Items"
            value={lowStockCount}
            subtitle="Items below minimum level"
            icon={<InventoryIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend={lowStockCount > 5 ? "up" : "down"}
            trendValue={lowStockCount > 5 ? "Alert" : "Good"}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Total Sales"
            value={`$${formatCurrency(totalSales)}`}
            subtitle="Today's collection"
            icon={<ShoppingBagIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="8%"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <JewelryStatBox
            title="Customers"
            value={totalCustomers}
            subtitle="Total customers"
            icon={<PersonIcon sx={{ color: colors.primary[500], fontSize: { xs: 22, sm: 26 } }} />}
            trend="up"
            trendValue="5%"
          />
        </Grid>
      </Grid>

      {/* CHARTS & TABLES */}
      <Grid container spacing={3}>
        {/* CATEGORY DISTRIBUTION */}
        <Grid item xs={12} md={4}>
          <Card sx={{
            height: '400px',
            p: 3,
            backgroundColor: colors.whiteAccent[100],
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            borderRadius: '12px'
          }}>
            <Typography variant="h5" fontWeight="600" sx={{ mb: 2, color: colors.blackAccent[700] }}>
              Inventory by Category
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {isLoadingRecent ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="300px">
                <CircularProgress size={40} sx={{ color: colors.primary[500] }} />
              </Box>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </Card>
        </Grid>

        {/* RECENT INVENTORY */}
        <Grid item xs={12} md={8}>
          <Card sx={{
            height: '400px',
            p: 3,
            backgroundColor: colors.whiteAccent[100],
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            borderRadius: '12px'
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h5" fontWeight="600" sx={{ color: colors.blackAccent[700] }}>
                Recent Inventory
              </Typography>
              <Button
                variant="text"
                onClick={() => navigate("/inventory")}
                sx={{
                  color: colors.primary[500],
                  '&:hover': { backgroundColor: 'transparent', color: colors.primary[600] }
                }}
              >
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />

            {isLoadingRecent ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="300px">
                <CircularProgress size={40} sx={{ color: colors.primary[500] }} />
              </Box>
            ) : (
              <Box sx={{ height: 300, width: '100%' }}>
                <DataGrid
                  rows={recentInventory?.items || []}
                  columns={recentInventoryColumns}
                  initialState={{
                    pagination: {
                      paginationModel: { pageSize: 5 },
                    },
                  }}
                  pageSizeOptions={[5]}
                  getRowId={(row) => row._id || row.id}
                  sx={{
                    '& .MuiDataGrid-root': { border: 'none' },
                    '& .MuiDataGrid-cell': { borderBottom: `1px solid ${colors.primary[100]}` },
                    '& .MuiDataGrid-columnHeaders': {
                      backgroundColor: colors.primary[100],
                      color: colors.primary[700],
                      borderBottom: 'none',
                    },
                    '& .MuiDataGrid-virtualScroller': { backgroundColor: 'transparent' },
                    '& .MuiDataGrid-footerContainer': {
                      borderTop: `1px solid ${colors.primary[100]}`,
                      backgroundColor: colors.primary[100],
                    },
                  }}
                />
              </Box>
            )}
          </Card>
        </Grid>

        {/* LOW STOCK ITEMS */}
        <Grid item xs={12}>
          <Card sx={{
            p: 3,
            backgroundColor: colors.whiteAccent[100],
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            borderRadius: '12px'
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h5" fontWeight="600" sx={{ color: colors.blackAccent[700] }}>
                Low Stock Items
              </Typography>
              <Button
                variant="text"
                onClick={() => navigate("/inventory?lowStock=true")}
                sx={{
                  color: colors.primary[500],
                  '&:hover': { backgroundColor: 'transparent', color: colors.primary[600] }
                }}
              >
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />

            {isLoadingLowStock ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="200px">
                <CircularProgress size={40} sx={{ color: colors.primary[500] }} />
              </Box>
            ) : lowStockItems?.items?.length === 0 ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="200px">
                <Typography variant="h6" sx={{ color: colors.blackAccent[500] }}>
                  No low stock items found
                </Typography>
              </Box>
            ) : (
              <Box sx={{ height: 300, width: '100%' }}>
                <DataGrid
                  rows={lowStockItems?.items || []}
                  columns={lowStockColumns}
                  initialState={{
                    pagination: {
                      paginationModel: { pageSize: 5 },
                    },
                  }}
                  pageSizeOptions={[5]}
                  getRowId={(row) => row._id || row.id}
                  sx={{
                    '& .MuiDataGrid-root': { border: 'none' },
                    '& .MuiDataGrid-cell': { borderBottom: `1px solid ${colors.primary[100]}` },
                    '& .MuiDataGrid-columnHeaders': {
                      backgroundColor: colors.primary[100],
                      color: colors.primary[700],
                      borderBottom: 'none',
                    },
                    '& .MuiDataGrid-virtualScroller': { backgroundColor: 'transparent' },
                    '& .MuiDataGrid-footerContainer': {
                      borderTop: `1px solid ${colors.primary[100]}`,
                      backgroundColor: colors.primary[100],
                    },
                  }}
                />
              </Box>
            )}
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default JewelryDashboard;
