# site info
SITE_TITLE=StarColors BMS
SITE_URL=http://localhost:2004
LOGO_PATH=/logo.png

# server config
NODE_ENV=development
SERVER_MODE=http
PORT=2004

# ssl info
SSL_KEY_PATH=
SSL_CERT_PATH=
SSL_CA_PATH=

# mongodb
MONGO_URI=

# e-mail config
# Allowed values: sendgrid, smtp
MAIL_SERVICE=
FROM_MAIL=
SENDGRID_API_KEY=
SMTP_HOST=
SMTP_PORT=
SMTP_SECURE=
TLS_CHECK=
SMTP_SERVICE=
SMTP_USERNAME=
SMTP_PASSWORD=

# aws s3 config
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=
AWS_S3_REGION=
AWS_S3_BUCKET=
AWS_S3_BASE=
AWS_S3_SECURE=

# bcrypt config
BCRYPT_ITERATIONS=

# jwt
JWT_SECRET=your-secret-key-here

# session (preventLoginOnFailedAttemptsTill in minutes)
allowedFailedLoginAttempts=
preventLoginOnFailedAttemptsTill=

# otp config
otpValidMinutes=

# notification
FCM_KEY=

# upload files configs
maxCatImgSize=
