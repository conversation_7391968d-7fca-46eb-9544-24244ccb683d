stages:
  - build
  - deploy

variables:
  NODE_ENV: production
  CI: "true"

build_frontend:
  stage: build
  image: node:20
  script:
    - cd dashboard
    - npm install --legacy-peer-deps --include=dev
    - npm run build
    
  artifacts:
    paths:
      - dashboard/dist

build_backend:
  stage: build
  image: node:20
  script:
    - cd backend
    - npm install

deploy:
  stage: deploy
  image: node:20
  only:
    - deployment
  before_script:
    - mkdir -p ~/.ssh
    - echo "SSH_PRIVATE_KEY path: $SSH_PRIVATE_KEY"
    - ls -l "$SSH_PRIVATE_KEY"
    - cp "$SSH_PRIVATE_KEY" ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - eval $(ssh-agent -s)
    - ssh-add ~/.ssh/id_rsa
  script:
    - echo "Deploying to server..."
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "
        cd /var/www/startcolorbms &&
        git pull origin deployment &&
        cd backend &&
        npm install &&
        pm2 reload all
      "
