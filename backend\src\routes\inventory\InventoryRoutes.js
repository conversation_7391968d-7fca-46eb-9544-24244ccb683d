const express = require('express');
const router = express.Router();
const InventoryController = require('./InventoryController');
const validations = require('./InventoryValidations');
const { validate } = require('../../util/validations');
const { verifyTokenAdmin } = require('../../util/auth');
const { checkPermission } = require('../../util/permissions');
const { Permissions } = require('../../../lib/models/enums');

// Public routes
router.get('/public', InventoryController.listInventorys);
router.get('/public/:id', validate(validations.requireId, 'params'), InventoryController.getInventoryById);

// Protected routes - require admin authentication
router.use(verifyTokenAdmin);

// Inventory management routes
router.post('/',
    checkPermission(Permissions.CREATE_INVENTORY),
    validate(validations.inventoryValidation),
    InventoryController.addInventory
);

router.post('/bulk',
    checkPermission(Permissions.CREATE_INVENTORY),
    validate(validations.bulkInventoryValidation),
    InventoryController.bulkAddInventory
);

router.put('/:id',
    checkPermission(Permissions.UPDATE_INVENTORY),
    validate(validations.requireId, 'params'),
    validate(validations.inventoryValidation),
    InventoryController.editInventory
);

router.delete('/:id',
    checkPermission(Permissions.DELETE_INVENTORY),
    validate(validations.requireId, 'params'),
    InventoryController.deleteInventory
);

router.get('/',
    checkPermission(Permissions.VIEW_INVENTORY),
    InventoryController.listInventorys
);

// Dashboard routes
router.get('/stats',
    checkPermission(Permissions.VIEW_INVENTORY),
    InventoryController.getInventoryStats
);

router.get('/categories',
    checkPermission(Permissions.VIEW_INVENTORY),
    InventoryController.getCategoryDistribution
);

router.get('/low-stock',
    checkPermission(Permissions.VIEW_INVENTORY),
    InventoryController.getLowStockItems
);

router.get('/:id',
    checkPermission(Permissions.VIEW_INVENTORY),
    validate(validations.requireId, 'params'),
    InventoryController.getInventoryById
);

module.exports = router;
