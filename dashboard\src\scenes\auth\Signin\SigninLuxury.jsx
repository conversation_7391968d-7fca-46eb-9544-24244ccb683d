import { useState } from "react";
import {
  Box,
  Button,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
  Typography,
  useTheme,
  Card,
  CardContent,
  Divider,
  styled,
  keyframes,
} from "@mui/material";
import { signInInitialValues, signInValidationSchema } from "constants/schemas";
import { useFormik } from "formik";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useSignInMutation } from "services";
import { setUserData } from "store/slices/authSlice";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";
import EmailOutlinedIcon from "@mui/icons-material/EmailOutlined";
import { tokens } from "../../../theme";

// Animation keyframes
const shimmer = keyframes`
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// Styled components
const GlossyCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  overflow: 'hidden',
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  animation: `${fadeIn} 0.8s ease-out`,
}));

const JewelryImage = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '100%',
  backgroundImage: 'url(https://images.unsplash.com/photo-1584302179602-e4c3d3fd629d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80)',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  borderRadius: '16px 0 0 16px',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, rgba(176, 141, 87, 0.7) 0%, rgba(92, 84, 112, 0.7) 100%)',
    borderRadius: 'inherit',
  },
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  zIndex: 2,
  textAlign: 'center',
  width: '80%',
}));

const ShimmerButton = styled(Button)(({ theme }) => ({
  background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 50%, ${theme.palette.primary.main} 100%)`,
  backgroundSize: '200% 100%',
  animation: `${shimmer} 2s infinite linear`,
  color: theme.palette.common.white,
  fontWeight: 600,
  padding: '12px 24px',
  borderRadius: '8px',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  boxShadow: '0 4px 15px rgba(176, 141, 87, 0.3)',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 8px 25px rgba(176, 141, 87, 0.4)',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    transition: 'transform 0.3s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
    },
    '& fieldset': {
      borderColor: theme.palette.mode === 'light' ? theme.palette.primary[300] : theme.palette.primary[700],
      transition: 'border-color 0.3s ease',
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary[500],
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary[500],
      borderWidth: '2px',
    },
  },
  '& .MuiInputLabel-root': {
    color: theme.palette.mode === 'light' ? theme.palette.primary[700] : theme.palette.primary[300],
    fontFamily: '"Inter", sans-serif',
  },
  '& .MuiInputBase-input': {
    padding: '16px 14px',
    fontFamily: '"Inter", sans-serif',
  },
}));

const SignIn = () => {
  // hooks
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  // mutation
  const [signIn] = useSignInMutation();

  const formik = useFormik({
    initialValues: signInInitialValues,
    validationSchema: signInValidationSchema,
    onSubmit: (values) => {
      signIn(values)
        .unwrap()
        .then((result) => {
          if (result?.token) {
            dispatch(setUserData(result));
            navigate("/");
          }
        });
    },
  });

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${colors.primary[900]} 0%, ${colors.blackAccent[700]} 100%)`,
        padding: { xs: 2, md: 4 },
        overflow: 'hidden',
        position: 'relative',
      }}
    >
      {/* Decorative elements - jewelry-inspired background elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${colors.primary[400]} 0%, transparent 70%)`,
          opacity: 0.3,
          filter: 'blur(40px)',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '15%',
          right: '10%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${colors.primary[500]} 0%, transparent 70%)`,
          opacity: 0.2,
          filter: 'blur(50px)',
        }}
      />

      <Container maxWidth="lg" sx={{ height: { md: '80vh' }, maxHeight: '900px' }}>
        <GlossyCard sx={{ height: '100%', display: 'flex', flexDirection: { xs: 'column', md: 'row' } }}>
          {/* Left Column - Image */}
          <Grid item xs={12} md={6} sx={{ display: { xs: 'none', md: 'block' }, position: 'relative' }}>
            <JewelryImage>
              <LogoContainer>
                {/* Star Colors Inc. Logo */}
                <Box sx={{ mb: 3, transform: 'scale(1.2)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 400 400">
                    <g fill="#1E3A8A">
                      <path d="M32.5,350 C32.5,350 45,310 75,290 C105,270 135,270 135,270 L135,290 C135,290 105,290 85,305 C65,320 55,350 55,350 L32.5,350 Z"/>
                      <path d="M135,270 C135,270 155,260 185,260 C215,260 235,270 235,270 L235,290 C235,290 215,280 185,280 C155,280 135,290 135,290 L135,270 Z"/>
                      <path d="M235,270 C235,270 265,270 295,290 C325,310 337.5,350 337.5,350 L315,350 C315,350 305,320 285,305 C265,290 235,290 235,290 L235,270 Z"/>
                      <path d="M32.5,50 C32.5,50 45,90 75,110 C105,130 135,130 135,130 L135,110 C135,110 105,110 85,95 C65,80 55,50 55,50 L32.5,50 Z"/>
                      <path d="M135,130 C135,130 155,140 185,140 C215,140 235,130 235,130 L235,110 C235,110 215,120 185,120 C155,120 135,110 135,110 L135,130 Z"/>
                      <path d="M235,130 C235,130 265,130 295,110 C325,90 337.5,50 337.5,50 L315,50 C315,50 305,80 285,95 C265,110 235,110 235,110 L235,130 Z"/>
                      <path d="M135,130 C135,130 125,160 125,200 C125,240 135,270 135,270 L135,290 C135,290 105,260 105,200 C105,140 135,110 135,110 L135,130 Z"/>
                      <path d="M235,130 C235,130 245,160 245,200 C245,240 235,270 235,270 L235,290 C235,290 265,260 265,200 C265,140 235,110 235,110 L235,130 Z"/>
                      
                      {/* Diamond shape */}
                      <path d="M350,150 L370,180 L350,210 L330,180 Z" fill="none" stroke="#1E3A8A" strokeWidth="2"/>
                      <path d="M350,160 L365,180 L350,200 L335,180 Z" fill="none" stroke="#1E3A8A" strokeWidth="1"/>
                      <line x1="350" y1="160" x2="350" y2="200" stroke="#1E3A8A" strokeWidth="1"/>
                      <line x1="335" y1="180" x2="365" y2="180" stroke="#1E3A8A" strokeWidth="1"/>
                    </g>
                  </svg>
                </Box>
                <Typography 
                  variant="h3" 
                  sx={{ 
                    color: '#fff', 
                    fontFamily: '"Playfair Display", serif',
                    textShadow: '0 2px 10px rgba(0,0,0,0.3)',
                    fontWeight: 600,
                    mb: 2
                  }}
                >
                  Star Colors Inc.
                </Typography>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    color: '#fff', 
                    opacity: 0.9,
                    fontFamily: '"Inter", sans-serif',
                    textShadow: '0 2px 5px rgba(0,0,0,0.2)',
                    fontWeight: 400,
                    maxWidth: '80%',
                    mx: 'auto'
                  }}
                >
                  Luxury Jewelry Management System
                </Typography>
              </LogoContainer>
            </JewelryImage>
          </Grid>

          {/* Right Column - Login Form */}
          <Grid item xs={12} md={6} sx={{ p: { xs: 3, sm: 4, md: 5 }, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Box sx={{ textAlign: 'center', mb: 4, display: { xs: 'block', md: 'none' } }}>
              {/* Logo for mobile view */}
              <Box sx={{ mb: 2, display: 'flex', justifyContent: 'center' }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 400 400">
                  <g fill={colors.primary[500]}>
                    <path d="M32.5,350 C32.5,350 45,310 75,290 C105,270 135,270 135,270 L135,290 C135,290 105,290 85,305 C65,320 55,350 55,350 L32.5,350 Z"/>
                    <path d="M135,270 C135,270 155,260 185,260 C215,260 235,270 235,270 L235,290 C235,290 215,280 185,280 C155,280 135,290 135,290 L135,270 Z"/>
                    <path d="M235,270 C235,270 265,270 295,290 C325,310 337.5,350 337.5,350 L315,350 C315,350 305,320 285,305 C265,290 235,290 235,290 L235,270 Z"/>
                    <path d="M32.5,50 C32.5,50 45,90 75,110 C105,130 135,130 135,130 L135,110 C135,110 105,110 85,95 C65,80 55,50 55,50 L32.5,50 Z"/>
                    <path d="M135,130 C135,130 155,140 185,140 C215,140 235,130 235,130 L235,110 C235,110 215,120 185,120 C155,120 135,110 135,110 L135,130 Z"/>
                    <path d="M235,130 C235,130 265,130 295,110 C325,90 337.5,50 337.5,50 L315,50 C315,50 305,80 285,95 C265,110 235,110 235,110 L235,130 Z"/>
                    <path d="M135,130 C135,130 125,160 125,200 C125,240 135,270 135,270 L135,290 C135,290 105,260 105,200 C105,140 135,110 135,110 L135,130 Z"/>
                    <path d="M235,130 C235,130 245,160 245,200 C245,240 235,270 235,270 L235,290 C235,290 265,260 265,200 C265,140 235,110 235,110 L235,130 Z"/>
                    
                    {/* Diamond shape */}
                    <path d="M350,150 L370,180 L350,210 L330,180 Z" fill="none" stroke={colors.primary[500]} strokeWidth="2"/>
                    <path d="M350,160 L365,180 L350,200 L335,180 Z" fill="none" stroke={colors.primary[500]} strokeWidth="1"/>
                    <line x1="350" y1="160" x2="350" y2="200" stroke={colors.primary[500]} strokeWidth="1"/>
                    <line x1="335" y1="180" x2="365" y2="180" stroke={colors.primary[500]} strokeWidth="1"/>
                  </g>
                </svg>
              </Box>
              <Typography variant="h4" sx={{ fontFamily: '"Playfair Display", serif', color: colors.primary[500], fontWeight: 600 }}>
                Star Colors Inc.
              </Typography>
            </Box>

            <Typography 
              variant="h3" 
              sx={{ 
                fontFamily: '"Playfair Display", serif', 
                color: colors.primary[500], 
                fontWeight: 500, 
                mb: 1,
                textAlign: { xs: 'center', md: 'left' }
              }}
            >
              Welcome Back
            </Typography>
            
            <Typography 
              variant="body1" 
              sx={{ 
                mb: 4, 
                color: colors.blackAccent[600],
                textAlign: { xs: 'center', md: 'left' }
              }}
            >
              Sign in to access your jewelry management dashboard
            </Typography>

            <Box component="form" onSubmit={formik.handleSubmit}>
              <StyledTextField
                fullWidth
                margin="normal"
                id="email"
                name="email"
                label="Email Address"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailOutlinedIcon sx={{ color: colors.primary[500] }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 3 }}
              />

              <StyledTextField
                fullWidth
                margin="normal"
                id="password"
                name="password"
                label="Password"
                type={showPassword ? "text" : "password"}
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.password && Boolean(formik.errors.password)}
                helperText={formik.touched.password && formik.errors.password}
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockOutlinedIcon sx={{ color: colors.primary[500] }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                        sx={{ color: colors.primary[500] }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 4 }}
              />

              <ShimmerButton
                type="submit"
                fullWidth
                variant="contained"
                sx={{ py: 1.5 }}
              >
                Sign In
              </ShimmerButton>

              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography variant="body2" sx={{ color: colors.blackAccent[500] }}>
                  {import.meta.env.VITE_APP_NAME || 'Star Colors Inc.'} © {new Date().getFullYear()}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </GlossyCard>
      </Container>
    </Box>
  );
};

export default SignIn;