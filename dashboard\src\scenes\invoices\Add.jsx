import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Divider,
  Grid,
  Typography,
  TextField,
  IconButton,
  useTheme,
  MenuItem,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import {
  useCreateInvoiceMutation,
  useUpdateInvoiceMutation,
  useGetInvoicesQuery,
} from "../../services/invoices.service";
import { useParams, useNavigate } from "react-router-dom";
import LuxuryButton from "components/LuxuryButton";
import Header from "components/Header";
import { useGetCustomersQuery, useGetInventoriesQuery } from "services";
import { toast } from "react-toastify";

const emptyItem = {
  product: "",
  name: "",
  quantity: 1,
  price: 0,
  gst: { rate: 0, amount: 0 },
};

const AddInvoice = () => {
  const theme = useTheme();
  const colors =
    theme.palette.mode === "dark"
      ? {
          primary: {
            500: "#d4af37",
            400: "#bfa14e",
            100: "#fff8e1",
            700: "#8d7b3c",
          },
          blackAccent: { 900: "#191919", 700: "#333", 500: "#666" },
        }
      : {
          primary: {
            500: "#bfa14e",
            400: "#d4af37",
            100: "#fff8e1",
            700: "#8d7b3c",
          },
          blackAccent: { 900: "#191919", 700: "#333", 500: "#666" },
        };
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  const { data: invoices = [] } = useGetInvoicesQuery();
  const { data: { items: customersData = [] } = {} } = useGetCustomersQuery();
  const { data: { items: inventoriesData = [] } = {} } =
    useGetInventoriesQuery();
  const invoice = isEdit
    ? invoices.find((inv) => String(inv.id) === String(id))
    : null;

  const [customer, setCustomer] = useState("");
  const [items, setItems] = useState([{ ...emptyItem }]);
  const [discount, setDiscount] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState("");
  const [createdBy, setCreatedBy] = useState("");
  const [
    createInvoice,
    {
      isLoading: isCreating,
      isSuccess: isCreateSuccess,
      isError: isCreateError,
      error: createError,
    },
  ] = useCreateInvoiceMutation();
  const [
    updateInvoice,
    {
      isLoading: isUpdating,
      isSuccess: isUpdateSuccess,
      isError: isUpdateError,
      error: updateError,
    },
  ] = useUpdateInvoiceMutation();

  useEffect(() => {
    if (isEdit && invoice) {
      setCustomer(invoice.customer || "");
      setItems(
        invoice.items && invoice.items.length > 0
          ? invoice.items
          : [{ ...emptyItem }]
      );
      setDiscount(invoice.discount || 0);
      setPaymentMethod(invoice.paymentMethod || "");
      setCreatedBy(invoice.createdBy || "");
    }
  }, [isEdit, invoice]);

  const handleItemChange = (idx, field, value) => {
    const updatedItems = items.map((item, i) =>
      i === idx ? { ...item, [field]: value } : item
    );
    setItems(updatedItems);
  };

  const handleGstChange = (idx, gstField, value) => {
    const updatedItems = items.map((item, i) =>
      i === idx ? { ...item, gst: { ...item.gst, [gstField]: value } } : item
    );
    setItems(updatedItems);
  };

  const addItem = () => setItems([...items, { ...emptyItem }]);
  const removeItem = (idx) => setItems(items.filter((_, i) => i !== idx));

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isEdit) {
      await updateInvoice({
        invoiceId: id,
        payload: { customer, items, discount, paymentMethod, createdBy },
      });
    } else {
      const res = await createInvoice({
        customer,
        items,
        discount,
        paymentMethod,
        createdBy,
      }).unwrap();
      console.log(res)
      if(res?.success){
        toast.success("Invoice created successfully");
        navigate('/invoices');
      }
    }
  };

  if (isEdit && !invoice) return <Typography>Invoice not found.</Typography>;

  // Custom styles for the form sections
  const sectionStyles = {
    card: {
      borderRadius: "12px",
      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.08)",
      overflow: "hidden",
      transition: "transform 0.3s ease, box-shadow 0.3s ease",
      height: "100%",
      mb: 3,
      "&:hover": {
        transform: "translateY(-5px)",
        boxShadow: "0px 8px 30px rgba(0, 0, 0, 0.12)",
      },
    },
    sectionTitle: {
      fontFamily: "Playfair Display, serif",
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: "relative",
      display: "inline-block",
      paddingBottom: "8px",
      "&::after": {
        content: '""',
        position: "absolute",
        width: "40%",
        height: "2px",
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
    itemsCard: {
      borderRadius: "12px",
      boxShadow: "0px 2px 10px rgba(212, 175, 55, 0.10)",
      background: theme.palette.mode === "dark" ? "#232323" : "#fffdf8",
      mb: 3,
      p: 2,
    },
    addBtn: {
      color: colors.primary[700],
      fontWeight: 600,
      fontFamily: "Playfair Display, serif",
      letterSpacing: 1,
      mb: 1,
    },
  };

  return (
    <Box m="20px">
      <Header
        title={isEdit ? "EDIT INVOICE" : "CREATE INVOICE"}
        subtitle={isEdit ? "Edit Invoice" : "Create a New Invoice"}
      />
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Invoice Details */}
          <Grid item xs={12}>
            <Card sx={sectionStyles.card}>
              <CardContent>
                <Typography variant="h4" sx={sectionStyles.sectionTitle}>
                  Invoice Information
                </Typography>
                <Divider sx={sectionStyles.divider} />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      select
                      label="Customer"
                      value={customer}
                      onChange={(e) => setCustomer(e.target.value)}
                      fullWidth
                      required
                      SelectProps={{
                        displayEmpty: true,
                        MenuProps: {
                          PaperProps: {
                            sx: {
                              borderRadius: 2,
                              boxShadow:
                                "0px 4px 20px rgba(212, 175, 55, 0.08)",
                              mt: 1,
                            },
                          },
                          sx: {
                            fontFamily: "Playfair Display, serif",
                          },
                        },
                      }}
                      sx={{
                        borderRadius: 2,
                        background: colors.primary[100],
                        fontFamily: "Playfair Display, serif",
                        "& .MuiSelect-select": {
                          fontWeight: 500,
                          color: colors.primary[700],
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: colors.primary[400],
                        },
                      }}
                      placeholder="Select a customer"
                      InputLabelProps={{
                        shrink: true,
                      }}
                    >
                      <MenuItem
                        value=""
                        disabled
                        sx={{
                          fontStyle: "italic",
                          color: colors.blackAccent[500],
                        }}
                      >
                        Select a customer
                      </MenuItem>
                      {customersData.map((cust) => (
                        <MenuItem
                          key={cust._id}
                          value={cust._id}
                          sx={{
                            fontFamily: "Playfair Display, serif",
                            color: colors.blackAccent[900],
                          }}
                        >
                          {cust.name} ({cust.email})
                        </MenuItem>
                      ))}
                    </TextField>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      select
                      label="Payment Method"
                      value={paymentMethod}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      fullWidth
                      required
                      SelectProps={{
                        displayEmpty: true,
                        MenuProps: {
                          PaperProps: {
                            sx: {
                              borderRadius: 2,
                              boxShadow:
                                "0px 4px 20px rgba(212, 175, 55, 0.08)",
                              mt: 1,
                            },
                          },
                          sx: {
                            fontFamily: "Playfair Display, serif",
                          },
                        },
                      }}
                      sx={{
                        borderRadius: 2,
                        background: colors.primary[100],
                        fontFamily: "Playfair Display, serif",
                        "& .MuiSelect-select": {
                          fontWeight: 500,
                          color: colors.primary[700],
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: colors.primary[400],
                        },
                      }}
                      placeholder="Select a payment method"
                      InputLabelProps={{ shrink: true }}
                    >
                      <MenuItem
                        value=""
                        disabled
                        sx={{
                          fontStyle: "italic",
                          color: colors.blackAccent[500],
                        }}
                      >
                        Select a payment method
                      </MenuItem>
                      <MenuItem value="cash">Cash</MenuItem>
                      <MenuItem value="card">Card</MenuItem>
                      <MenuItem value="upi">UPI</MenuItem>
                      <MenuItem value="netbanking">Netbanking</MenuItem>
                      <MenuItem value="other">Other</MenuItem>
                    </TextField>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Discount"
                      type="number"
                      value={discount}
                      onChange={(e) => setDiscount(Number(e.target.value))}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Created By"
                      value={createdBy}
                      onChange={(e) => setCreatedBy(e.target.value)}
                      fullWidth
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Items Section */}
          <Grid item xs={12}>
            <Card sx={sectionStyles.itemsCard}>
              <CardContent>
                <Typography variant="h5" sx={sectionStyles.sectionTitle}>
                  Items
                </Typography>
                <Divider sx={sectionStyles.divider} />
                {items.map((item, idx) => (
                  <Box
                    key={idx}
                    sx={{
                      p: 2,
                      mb: 2,
                      background: "#f9f9f9",
                      borderRadius: 2,
                      boxShadow: 0,
                    }}
                  >
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3}>
                        <TextField
                          select
                          label="Product"
                          value={item.product}
                          onChange={(e) =>
                            handleItemChange(idx, "product", e.target.value)
                          }
                          fullWidth
                          required
                          SelectProps={{
                            displayEmpty: true,
                            MenuProps: {
                              PaperProps: {
                                sx: {
                                  borderRadius: 2,
                                  boxShadow:
                                    "0px 4px 20px rgba(212, 175, 55, 0.08)",
                                  mt: 1,
                                },
                              },
                              sx: {
                                fontFamily: "Playfair Display, serif",
                              },
                            },
                          }}
                          sx={{
                            borderRadius: 2,
                            background: colors.primary[100],
                            fontFamily: "Playfair Display, serif",
                            "& .MuiSelect-select": {
                              fontWeight: 500,
                              color: colors.primary[700],
                            },
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: colors.primary[400],
                            },
                          }}
                          placeholder="Select a product"
                          InputLabelProps={{ shrink: true }}
                        >
                          <MenuItem
                            value=""
                            disabled
                            sx={{
                              fontStyle: "italic",
                              color: colors.blackAccent[500],
                            }}
                          >
                            Select a product
                          </MenuItem>
                          {inventoriesData.map((inv) => (
                            <MenuItem
                              key={inv._id}
                              value={inv._id}
                              sx={{
                                fontFamily: "Playfair Display, serif",
                                color: colors.blackAccent[900],
                              }}
                            >
                              {inv.name} ({inv.sku})
                            </MenuItem>
                          ))}
                        </TextField>
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          label="Name"
                          value={item.name}
                          onChange={(e) =>
                            handleItemChange(idx, "name", e.target.value)
                          }
                          fullWidth
                          required
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          label="Quantity"
                          type="number"
                          value={item.quantity}
                          onChange={(e) =>
                            handleItemChange(
                              idx,
                              "quantity",
                              Number(e.target.value)
                            )
                          }
                          fullWidth
                          required
                          inputProps={{ min: 1 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          label="Price"
                          type="number"
                          value={item.price}
                          onChange={(e) =>
                            handleItemChange(
                              idx,
                              "price",
                              Number(e.target.value)
                            )
                          }
                          fullWidth
                          required
                          inputProps={{ min: 0 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          label="GST Rate (%)"
                          type="number"
                          value={item.gst.rate}
                          onChange={(e) =>
                            handleGstChange(idx, "rate", Number(e.target.value))
                          }
                          fullWidth
                        />
                        <TextField
                          label="GST Amount"
                          type="number"
                          value={item.gst.amount}
                          onChange={(e) =>
                            handleGstChange(
                              idx,
                              "amount",
                              Number(e.target.value)
                            )
                          }
                          fullWidth
                          sx={{ mt: 1 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={1}>
                        <IconButton
                          onClick={() => removeItem(idx)}
                          disabled={items.length === 1}
                        >
                          <RemoveIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </Box>
                ))}
                <LuxuryButton
                  startIcon={<AddIcon />}
                  onClick={addItem}
                  variant="outlined"
                  sx={sectionStyles.addBtn}
                >
                  Add Item
                </LuxuryButton>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12}>
            <LuxuryButton
              type="submit"
              size="large"
              disabled={isCreating || isUpdating}
              fullWidth
            >
              {isEdit
                ? isUpdating
                  ? "Updating..."
                  : "Update Invoice"
                : isCreating
                ? "Creating..."
                : "Create Invoice"}
            </LuxuryButton>
            {isEdit && isUpdateSuccess && (
              <Typography color="success.main" mt={2}>
                Invoice updated successfully!
              </Typography>
            )}
            {!isEdit && isCreateSuccess && (
              <Typography color="success.main" mt={2}>
                Invoice created successfully!
              </Typography>
            )}
            {isEdit && isUpdateError && (
              <Typography color="error" mt={2}>
                {updateError?.data?.message || "Failed to update invoice."}
              </Typography>
            )}
            {!isEdit && isCreateError && (
              <Typography color="error" mt={2}>
                {createError?.data?.message || "Failed to create invoice."}
              </Typography>
            )}
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default AddInvoice;
