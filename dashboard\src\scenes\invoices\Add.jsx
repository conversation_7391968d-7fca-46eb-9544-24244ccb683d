import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Grid,
  Typography,
  TextField,
  IconButton,
  useTheme,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Snackbar,
  Alert,
  Stack,
  Tooltip,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SaveIcon from "@mui/icons-material/Save";
import {
  useCreateInvoiceMutation,
  useUpdateInvoiceMutation,
  useGetInvoicesQuery,
} from "../../services/invoices.service";
import { useParams, useNavigate } from "react-router-dom";
import LuxuryButton from "components/LuxuryButton";
import Header from "components/Header";
import { useGetCustomersQuery, useGetInventoriesQuery } from "services";
import { toast } from "react-toastify";
import { formatMediaUrl } from "utils/common";

const emptyItem = {
  product: "",
  name: "",
  quantity: 1,
  price: 0,
  weight: "",
  gst: { rate: 0, amount: 0 },
};

const AddInvoice = () => {
  const theme = useTheme();
  const invoiceRef = useRef(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: "", severity: "success" });

  // Invoice term options
  const termOptions = ["Net", "Net 15", "Net 30", "Net 60", "Net 90"];

  // Ship via options
  const shipViaOptions = ["UPS", "FedEx", "DHL", "USPS"];

  // Luxury jewelry color scheme (matching View component)
  const colors =
    theme.palette.mode === "dark"
      ? {
          primary: {
            500: "#C9A063", // Gold
            400: "#D4B77B", // Lighter gold
            100: "#F9F7F4", // Soft white
            700: "#B08D57", // Darker gold
          },
          blackAccent: { 900: "#191919", 700: "#7D7D7D", 500: "#A9A9A9" }, // Accent gray shades
        }
      : {
          primary: {
            500: "#C9A063", // Gold
            400: "#D4B77B", // Lighter gold
            100: "#F9F7F4", // Soft white
            700: "#B08D57", // Darker gold
          },
          blackAccent: { 900: "#191919", 700: "#7D7D7D", 500: "#A9A9A9" }, // Accent gray shades
        };

  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  const { data: invoices = [] } = useGetInvoicesQuery();
  const { data: { items: customersData = [] } = {} } = useGetCustomersQuery();
  const { data: { items: inventoriesData = [] } = {} } =
    useGetInventoriesQuery();
  const invoice = isEdit
    ? invoices.find((inv) => String(inv.id) === String(id))
    : null;

  // Form state
  const [customer, setCustomer] = useState("");
  const [items, setItems] = useState([{ ...emptyItem }]);
  const [discount, setDiscount] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState("");
  const [createdBy, setCreatedBy] = useState("");

  // Additional invoice fields
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [invoiceDate, setInvoiceDate] = useState(new Date().toISOString().split('T')[0]);
  const [poNumber, setPoNumber] = useState("SPECIAL ORDER");
  const [terms, setTerms] = useState("Net");
  const [rep, setRep] = useState("ANKUS");
  const [shippingDate, setShippingDate] = useState(new Date().toISOString().split('T')[0]);
  const [shipVia, setShipVia] = useState("FedEx");
  const [
    createInvoice,
    {
      isLoading: isCreating,
      isSuccess: isCreateSuccess,
      isError: isCreateError,
      error: createError,
    },
  ] = useCreateInvoiceMutation();
  const [
    updateInvoice,
    {
      isLoading: isUpdating,
      isSuccess: isUpdateSuccess,
      isError: isUpdateError,
      error: updateError,
    },
  ] = useUpdateInvoiceMutation();

  useEffect(() => {
    if (isEdit && invoice) {
      setCustomer(invoice.customer || "");
      setItems(
        invoice.items && invoice.items.length > 0
          ? invoice.items.map(item => ({
              ...item,
              weight: item.weight || ""
            }))
          : [{ ...emptyItem }]
      );
      setDiscount(invoice.discount || 0);
      setPaymentMethod(invoice.paymentMethod || "");
      setCreatedBy(invoice.createdBy || "");
      setInvoiceNumber(invoice.invoiceNumber || "");
      setInvoiceDate(invoice.createdAt ? new Date(invoice.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]);
      setPoNumber(invoice.poNumber || "SPECIAL ORDER");
      setTerms(invoice.terms || "Net");
      setRep(invoice.rep || "ANKUS");
      setShippingDate(invoice.shippingDate ? new Date(invoice.shippingDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]);
      setShipVia(invoice.shipVia || "FedEx");
    }
  }, [isEdit, invoice]);

  const handleItemChange = (idx, field, value) => {
    const updatedItems = items.map((item, i) =>
      i === idx ? { ...item, [field]: value } : item
    );
    setItems(updatedItems);
  };

  const handleGstChange = (idx, gstField, value) => {
    const updatedItems = items.map((item, i) =>
      i === idx ? { ...item, gst: { ...item.gst, [gstField]: value } } : item
    );
    setItems(updatedItems);
  };

  const addItem = () => setItems([...items, { ...emptyItem }]);
  const removeItem = (idx) => setItems(items.filter((_, i) => i !== idx));

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const gstTotal = items.reduce((sum, item) => sum + (item.gst?.amount || 0), 0);
    const total = subtotal + gstTotal - discount;
    return { subtotal, gstTotal, total };
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const { subtotal, gstTotal, total } = calculateTotals();

      const payload = {
        customer,
        items,
        discount,
        paymentMethod,
        createdBy,
        invoiceNumber,
        poNumber,
        terms,
        rep,
        shippingDate,
        shipVia,
        subtotal,
        gstTotal,
        total
      };

      if (isEdit) {
        const res = await updateInvoice({
          invoiceId: id,
          payload,
        }).unwrap();
        if (res?.success) {
          setSnackbar({ open: true, message: "Invoice updated successfully!", severity: "success" });
          setTimeout(() => navigate('/invoices'), 2000);
        }
      } else {
        const res = await createInvoice(payload).unwrap();
        if (res?.success) {
          setSnackbar({ open: true, message: "Invoice created successfully!", severity: "success" });
          setTimeout(() => navigate('/invoices'), 2000);
        }
      }
    } catch (error) {
      console.error('Error saving invoice:', error);
      setSnackbar({
        open: true,
        message: `Failed to ${isEdit ? 'update' : 'create'} invoice`,
        severity: "error"
      });
    }
  };

  if (isEdit && !invoice) return <Typography>Invoice not found.</Typography>;

  // Get selected customer data
  const selectedCustomer = customersData.find(cust => cust._id === customer);

  // Styles matching the View component
  const viewStyles = {
    invoiceHeader: {
      background: `linear-gradient(135deg, ${colors.primary[100]}, ${colors.primary[400]})`,
      p: 4,
      borderRadius: "12px",
      mb: 4,
      position: "relative",
      overflow: "hidden",
      boxShadow: "0px 10px 30px rgba(201, 160, 99, 0.15)",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        right: 0,
        width: "250px",
        height: "250px",
        background: `radial-gradient(circle, ${colors.primary[500]} 0%, transparent 70%)`,
        opacity: 0.15,
      },
      "&::after": {
        content: '""',
        position: "absolute",
        bottom: 0,
        left: 0,
        width: "200px",
        height: "200px",
        background: `radial-gradient(circle, ${colors.primary[500]} 0%, transparent 70%)`,
        opacity: 0.1,
      },
    },
    backBtn: {
      mb: 2,
      color: colors.primary[700],
      fontWeight: 600,
      fontFamily: "Playfair Display, serif",
      letterSpacing: 1,
      transition: "all 0.3s ease",
      "&:hover": {
        transform: "translateX(-5px)",
      },
    },
    actionButton: {
      color: colors.primary[700],
      backgroundColor: "transparent",
      border: `1px solid ${colors.primary[400]}`,
      borderRadius: "50%",
      padding: "8px",
      transition: "all 0.3s ease",
      "&:hover": {
        color: "#FFFFFF",
        backgroundColor: colors.primary[500],
        transform: "scale(1.1)",
        boxShadow: "0px 4px 10px rgba(201, 160, 99, 0.3)",
      },
    },
  };

  return (
    <Box m="24px">
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={4}
        sx={{
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 2, sm: 0 },
          alignItems: { xs: "flex-start", sm: "center" },
        }}
      >
        <Header
          title={isEdit ? `Edit Invoice ${invoiceNumber ? `#${invoiceNumber}` : ''}` : "Create Invoice"}
          subtitle={isEdit ? "Edit invoice details" : "Create a new invoice"}
        />
        <Stack
          direction="row"
          spacing={2}
          sx={{
            flexWrap: { xs: "wrap", md: "nowrap" },
            justifyContent: { xs: "flex-start", sm: "flex-end" },
            gap: 1
          }}
        >
          <Tooltip title="Save Invoice" arrow placement="top">
            <IconButton
              onClick={handleSubmit}
              sx={viewStyles.actionButton}
              disabled={isCreating || isUpdating}
            >
              <SaveIcon />
            </IconButton>
          </Tooltip>
          <LuxuryButton
            onClick={() => navigate(-1)}
            startIcon={<ArrowBackIcon />}
            variant="outlined"
            sx={viewStyles.backBtn}
          >
            Back to Invoices
          </LuxuryButton>
        </Stack>
      </Box>

      <form onSubmit={handleSubmit}>
        <Box ref={invoiceRef}>
          {/* Company Header */}
          <Paper sx={viewStyles.invoiceHeader} elevation={3}>
            <Grid container spacing={2}>
              {/* Company Info and Logo - Left Side */}
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <img
                    src={formatMediaUrl("/images/logo.png")}
                    alt="GD Colors Inc"
                    style={{ width: '50px', height: '50px', marginRight: '15px' }}
                  />
                  <Typography sx={{
                    fontFamily: "Playfair Display, serif",
                    fontSize: "1.8rem",
                    fontWeight: 700,
                    color: colors.primary[700],
                  }}>
                    GD COLORS INC
                  </Typography>
                </Box>
                <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                  38 West 48 Street, Suite # 1004
                </Typography>
                <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                  New York, NY 10036
                </Typography>
                <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                  Tel: (212)391-7799 Fax: (212)391-7797
                </Typography>
                <Typography sx={{ fontSize: '0.9rem', mb: 0.5 }}>
                  Email: <EMAIL>
                </Typography>
              </Grid>

              {/* Invoice Title and Details - Right Side */}
              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: { xs: 'left', md: 'right' }, mb: 2 }}>
                  <Typography variant="h4" sx={{
                    fontFamily: "Playfair Display, serif",
                    fontSize: "2rem",
                    fontWeight: 700,
                    color: colors.primary[700],
                    mb: 2
                  }}>
                    Invoice
                  </Typography>

                  <Box sx={{
                    border: '1px solid #ccc',
                    display: 'inline-block',
                    width: { xs: '100%', md: '200px' }
                  }}>
                    <Grid container>
                      <Grid item xs={6} sx={{ borderRight: '1px solid #ccc', p: 1, borderBottom: '1px solid #ccc' }}>
                        <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Date</Typography>
                      </Grid>
                      <Grid item xs={6} sx={{ p: 1, borderBottom: '1px solid #ccc' }}>
                        <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Invoice #</Typography>
                      </Grid>
                      <Grid item xs={6} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
                        <TextField
                          type="date"
                          value={invoiceDate}
                          onChange={(e) => setInvoiceDate(e.target.value)}
                          variant="standard"
                          InputProps={{ disableUnderline: true }}
                          sx={{ fontSize: '0.9rem', width: '100%' }}
                        />
                      </Grid>
                      <Grid item xs={6} sx={{ p: 1 }}>
                        <TextField
                          value={invoiceNumber}
                          onChange={(e) => setInvoiceNumber(e.target.value)}
                          placeholder="Auto-generated"
                          variant="standard"
                          InputProps={{ disableUnderline: true }}
                          sx={{ fontSize: '0.9rem', width: '100%' }}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Bill To and Ship To */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Box sx={{
                border: '1px solid #ccc',
                p: 2,
                height: '100%',
                borderRadius: '4px'
              }}>
                <Typography sx={{ fontWeight: 600, mb: 1 }}>Bill To</Typography>
                <TextField
                  select
                  label="Customer"
                  value={customer}
                  onChange={(e) => setCustomer(e.target.value)}
                  fullWidth
                  required
                  variant="standard"
                  InputProps={{ disableUnderline: true }}
                  sx={{ mb: 1 }}
                >
                  <MenuItem value="" disabled>
                    Select a customer
                  </MenuItem>
                  {customersData.map((cust) => (
                    <MenuItem key={cust._id} value={cust._id}>
                      {cust.name} ({cust.email})
                    </MenuItem>
                  ))}
                </TextField>
                {selectedCustomer && (
                  <>
                    <Typography>{selectedCustomer.name}</Typography>
                    <Typography>{selectedCustomer.address?.line1 || ""}</Typography>
                    <Typography>{selectedCustomer.address?.line2 || ""}</Typography>
                    <Typography>
                      {selectedCustomer.address?.city ? `${selectedCustomer.address.city}, ` : ""}
                      {selectedCustomer.address?.state || ""}
                      {selectedCustomer.address?.zipCode ? ` ${selectedCustomer.address.zipCode}` : ""}
                    </Typography>
                    <Typography>{selectedCustomer.email || ""}</Typography>
                    <Typography>{selectedCustomer.phone || ""}</Typography>
                  </>
                )}
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{
                border: '1px solid #ccc',
                p: 2,
                height: '100%',
                borderRadius: '4px'
              }}>
                <Typography sx={{ fontWeight: 600, mb: 1 }}>Ship To</Typography>
                {selectedCustomer && (
                  <>
                    <Typography>{selectedCustomer.shippingAddress?.name || selectedCustomer.name || "N/A"}</Typography>
                    <Typography>{selectedCustomer.shippingAddress?.line1 || selectedCustomer.address?.line1 || ""}</Typography>
                    <Typography>{selectedCustomer.shippingAddress?.line2 || selectedCustomer.address?.line2 || ""}</Typography>
                    <Typography>
                      {selectedCustomer.shippingAddress?.city ? `${selectedCustomer.shippingAddress.city}, ` :
                       selectedCustomer.address?.city ? `${selectedCustomer.address.city}, ` : ""}
                      {selectedCustomer.shippingAddress?.state || selectedCustomer.address?.state || ""}
                      {selectedCustomer.shippingAddress?.zipCode ? ` ${selectedCustomer.shippingAddress.zipCode}` :
                       selectedCustomer.address?.zipCode ? ` ${selectedCustomer.address.zipCode}` : ""}
                    </Typography>
                  </>
                )}
              </Box>
            </Grid>
          </Grid>

          {/* Order Info */}
          <Box sx={{
            border: '1px solid #ccc',
            mb: 3,
            borderRadius: '4px'
          }}>
            <Grid container>
              <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
                <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>P.O. Number</Typography>
              </Grid>
              <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
                <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Terms</Typography>
              </Grid>
              <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
                <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Rep</Typography>
              </Grid>
              <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', p: 1 }}>
                <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Ship</Typography>
              </Grid>
              <Grid item xs={2} sx={{ p: 1 }}>
                <Typography sx={{ fontWeight: 600, fontSize: '0.9rem' }}>Ship Via</Typography>
              </Grid>

              <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
                <TextField
                  value={poNumber}
                  onChange={(e) => setPoNumber(e.target.value)}
                  variant="standard"
                  InputProps={{ disableUnderline: true }}
                  sx={{ fontSize: '0.9rem', width: '100%' }}
                />
              </Grid>
              <Grid item xs={3} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
                <TextField
                  select
                  value={terms}
                  onChange={(e) => setTerms(e.target.value)}
                  variant="standard"
                  InputProps={{ disableUnderline: true }}
                  sx={{ fontSize: '0.9rem', width: '100%' }}
                >
                  {termOptions.map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
                <TextField
                  value={rep}
                  onChange={(e) => setRep(e.target.value)}
                  variant="standard"
                  InputProps={{ disableUnderline: true }}
                  sx={{ fontSize: '0.9rem', width: '100%' }}
                />
              </Grid>
              <Grid item xs={2} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc', p: 1 }}>
                <TextField
                  type="date"
                  value={shippingDate}
                  onChange={(e) => setShippingDate(e.target.value)}
                  variant="standard"
                  InputProps={{ disableUnderline: true }}
                  sx={{ fontSize: '0.9rem', width: '100%' }}
                />
              </Grid>
              <Grid item xs={2} sx={{ borderTop: '1px solid #ccc', p: 1 }}>
                <TextField
                  select
                  value={shipVia}
                  onChange={(e) => setShipVia(e.target.value)}
                  variant="standard"
                  InputProps={{ disableUnderline: true }}
                  sx={{ fontSize: '0.9rem', width: '100%' }}
                >
                  {shipViaOptions.map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
            </Grid>
          </Box>

          {/* Items Table */}
          <Box sx={{ mb: 3 }}>
            <TableContainer sx={{
              border: '1px solid #ccc',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '50px'
                    }}>Lot #</TableCell>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '40%'
                    }}>Description</TableCell>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '80px'
                    }}>Pieces</TableCell>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '100px'
                    }}>Weight/Qty</TableCell>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '100px'
                    }}>Price Per Unit</TableCell>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '120px'
                    }}>Amount in USD</TableCell>
                    <TableCell sx={{
                      fontWeight: 600,
                      backgroundColor: '#f5f5f5',
                      width: '50px'
                    }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index} hover>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                        {index + 1}
                      </TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                        <TextField
                          value={item.name}
                          onChange={(e) => handleItemChange(index, "name", e.target.value)}
                          variant="standard"
                          InputProps={{ disableUnderline: true }}
                          placeholder="Item description"
                          fullWidth
                          required
                        />
                      </TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                        <TextField
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleItemChange(index, "quantity", Number(e.target.value))}
                          variant="standard"
                          InputProps={{ disableUnderline: true }}
                          inputProps={{ min: 1 }}
                          fullWidth
                          required
                        />
                      </TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                        <TextField
                          value={item.weight}
                          onChange={(e) => handleItemChange(index, "weight", e.target.value)}
                          variant="standard"
                          InputProps={{ disableUnderline: true }}
                          placeholder="Weight"
                          fullWidth
                        />
                      </TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                        <TextField
                          type="number"
                          value={item.price}
                          onChange={(e) => handleItemChange(index, "price", Number(e.target.value))}
                          variant="standard"
                          InputProps={{ disableUnderline: true }}
                          inputProps={{ min: 0, step: 0.01 }}
                          fullWidth
                          required
                        />
                      </TableCell>
                      <TableCell sx={{
                        borderBottom: '1px solid #ccc',
                        fontWeight: 600
                      }}>
                        ${(item.price * item.quantity).toFixed(2)}
                      </TableCell>
                      <TableCell sx={{ borderBottom: '1px solid #ccc' }}>
                        <IconButton
                          onClick={() => removeItem(index)}
                          disabled={items.length === 1}
                          size="small"
                        >
                          <RemoveIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {/* Add empty rows if needed */}
                  {items.length < 5 && (
                    Array.from({ length: 5 - items.length }).map((_, index) => (
                      <TableRow key={`empty-${index}`}>
                        <TableCell sx={{ borderBottom: '1px solid #ccc', height: '30px' }}></TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #ccc' }}></TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-start' }}>
              <LuxuryButton
                startIcon={<AddIcon />}
                onClick={addItem}
                variant="outlined"
                sx={{
                  color: colors.primary[700],
                  fontWeight: 600,
                  fontFamily: "Playfair Display, serif",
                  letterSpacing: 1,
                }}
              >
                Add Item
              </LuxuryButton>
            </Box>
          </Box>

          {/* Additional Fields */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Payment Method"
                select
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                fullWidth
                required
              >
                <MenuItem value="cash">Cash</MenuItem>
                <MenuItem value="card">Card</MenuItem>
                <MenuItem value="upi">UPI</MenuItem>
                <MenuItem value="netbanking">Netbanking</MenuItem>
                <MenuItem value="other">Other</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Created By"
                value={createdBy}
                onChange={(e) => setCreatedBy(e.target.value)}
                fullWidth
              />
            </Grid>
          </Grid>

          {/* Thank you message */}
          <Box sx={{
            border: '1px solid #ccc',
            p: 2,
            mb: 3,
            borderRadius: '4px',
            fontSize: '0.9rem'
          }}>
            <Typography sx={{ mb: 1 }}>Thank you for your business.</Typography>
            <Typography sx={{ mb: 1 }}>
              Terms: For resale only. Any discrepancies in date, weight & rate must be informed within 10 days.
              All overdue invoices are subject to finance charge @ 2% per month. Our liability is limited to the price of the product.
              Unless otherwise stated, all color stones have been subjected to a stable and possibly undetectable color enhancement
              process. Prevailing market values are based on those universally practiced and accepted process by the gem and jewelry trade.
              More information can be provided upon your request. Thank you for your business.
            </Typography>
          </Box>

          {/* Totals */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={7}>
              {/* Signature section can go here if needed */}
            </Grid>

            <Grid item xs={12} md={5}>
              <Box sx={{
                border: '1px solid #ccc',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <Grid container>
                  <Grid item xs={8} sx={{
                    borderRight: '1px solid #ccc',
                    borderBottom: '1px solid #ccc',
                    p: 1,
                    backgroundColor: '#f5f5f5',
                    fontWeight: 600
                  }}>
                    Subtotal in USD
                  </Grid>
                  <Grid item xs={4} sx={{
                    borderBottom: '1px solid #ccc',
                    p: 1,
                    textAlign: 'right',
                    fontWeight: 600
                  }}>
                    ${calculateTotals().subtotal.toFixed(2)}
                  </Grid>

                  <Grid item xs={8} sx={{
                    borderRight: '1px solid #ccc',
                    borderBottom: '1px solid #ccc',
                    p: 1,
                    backgroundColor: '#f5f5f5',
                    fontWeight: 600
                  }}>
                    Discount
                  </Grid>
                  <Grid item xs={4} sx={{
                    borderBottom: '1px solid #ccc',
                    p: 1,
                    textAlign: 'right'
                  }}>
                    <TextField
                      type="number"
                      value={discount}
                      onChange={(e) => setDiscount(Number(e.target.value))}
                      variant="standard"
                      InputProps={{ disableUnderline: true }}
                      inputProps={{ min: 0, step: 0.01 }}
                      sx={{ textAlign: 'right', width: '100%' }}
                    />
                  </Grid>

                  <Grid item xs={8} sx={{
                    borderRight: '1px solid #ccc',
                    p: 1,
                    backgroundColor: '#f5f5f5',
                    fontWeight: 600
                  }}>
                    Total in USD
                  </Grid>
                  <Grid item xs={4} sx={{
                    p: 1,
                    textAlign: 'right',
                    fontWeight: 600
                  }}>
                    ${calculateTotals().total.toFixed(2)}
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>

        </Box>

        {/* Submit Button */}
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
          <LuxuryButton
            type="submit"
            size="large"
            disabled={isCreating || isUpdating}
            sx={{ minWidth: 200 }}
          >
            {isEdit
              ? isUpdating
                ? "Updating..."
                : "Update Invoice"
              : isCreating
              ? "Creating..."
              : "Create Invoice"}
          </LuxuryButton>
        </Box>
      </form>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            width: "100%",
            border: `1px solid ${colors.primary[400]}`,
            boxShadow: "0px 4px 12px rgba(201, 160, 99, 0.15)",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AddInvoice;
