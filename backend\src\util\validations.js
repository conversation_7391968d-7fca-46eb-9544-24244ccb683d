const Joi = require('joi');
const mongoose = require('mongoose');

// Create custom Joi ObjectId validation
const ExtendedJoi = Joi.extend((joi) => ({
    type: 'objectId',
    base: joi.string(),
    messages: {
        'objectId.invalid': 'Invalid ObjectId format'
    },
    validate(value, helpers) {
        if (!mongoose.Types.ObjectId.isValid(value)) {
            return { value, errors: helpers.error('objectId.invalid') };
        }
        return { value };  // Return value if validation passes
    }
}));

// Patterns for validation
const patterns = {
    password: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/,
    adminPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    number: /^\d+$/,
};

// Common validation schemas
const common = {
    email: ExtendedJoi.string()
        .trim()
        .lowercase()
        .email()
        .required()
        .messages({
            'string.email': 'Please enter a valid email address',
            'any.required': 'Email is required'
        }),
    adminPassword: ExtendedJoi.string()
        .trim()
        .pattern(patterns.adminPassword)
        .required()
        .messages({
            'string.pattern.base': 'Password must contain at least 8 characters with uppercase, lowercase, number and special character',
            'any.required': 'Password is required'
        }),
    password: ExtendedJoi.string()
        .trim()
        .pattern(patterns.password)
        .required()
        .messages({
            'string.pattern.base': 'Password must contain at least 8 characters with letters and numbers',
            'any.required': 'Password is required'
        }),
    age: ExtendedJoi.number()
        .integer()
        .min(13)
        .max(120)
        .required()
        .messages({
            'number.base': 'Age must be a number',
            'number.min': 'Age must be at least 13 years',
            'number.max': 'Age must be less than 120 years',
            'any.required': 'Age is required'
        }),
    countryCode: ExtendedJoi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Country code is required'
        }),
    phone: ExtendedJoi.string()
        .trim()
        .min(10)
        .max(15)
        .required()
        .messages({
            'string.min': 'Phone number must be at least 10 digits',
            'string.max': 'Phone number cannot exceed 15 digits',
            'any.required': 'Phone number is required'
        }),
    otp: ExtendedJoi.string()
        .trim()
        .length(6)
        .pattern(/^\d+$/)
        .required()
        .messages({
            'string.length': 'OTP must be 6 digits',
            'string.pattern.base': 'OTP must contain only numbers',
            'any.required': 'OTP is required'
        }),
    page: ExtendedJoi.number()
        .integer()
        .min(1)
        .default(1),
    perPage: ExtendedJoi.number()
        .integer()
        .min(1)
        .max(100)
        .default(10)
};

// Validation middleware
const validate = (schema, source = 'body') => {
    return (req, res, next) => {
        try {
            const { error, value } = schema.validate(req[source], {
                abortEarly: false,
                stripUnknown: true
            });

            if (error) {
                return res.status(400).json({
                    success: false,
                    message: error.details[0].message,
                    errors: error.details.map(detail => detail.message)
                });
            }

            req[source] = value;
            next();
        } catch (err) {
            console.error('Validation error:', err);
            return res.status(500).json({
                success: false,
                message: 'Validation error occurred'
            });
        }
    };
};

module.exports = {
    Joi: ExtendedJoi,
    patterns,
    common,
    validate
};
