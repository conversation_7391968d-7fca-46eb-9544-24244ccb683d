import { API_ENDPOINTS } from "../constants/endpoints";
import { apiSlice, handlePostResponse, handleResponse } from "./base.service";

export const customersApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCustomers: builder.query({
      query: () => ({
        url: API_ENDPOINTS.GET_CUSTOMERS,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    addCustomer: builder.mutation({
      query: (payload) => ({
        url: API_ENDPOINTS.CREATE_CUSTOMER,
        method: "POST",
        body: payload,
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),
    updateCustomer: builder.mutation({
      query: ({payload, customerId}) => ({
        url: `${API_ENDPOINTS.UPDATE_CUSTOMER}/${customerId}`,
        method: "PUT",
        body: payload,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    getCustomerInfo: builder.query({
      query: (customerId) => ({
        url: `${API_ENDPOINTS.GET_CUSTOMER_INFO}/${customerId}`,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    deleteCustomer: builder.mutation({
      query: ({ id }) => ({
        url: `${API_ENDPOINTS.DELETE_CUSTOMER}/${id}`,
        method: "DELETE",
      }),
      transformResponse: handlePostResponse,
      transformErrorResponse: handleResponse,
    }),
  }),
});

export const {
  useGetCustomersQuery,
  useAddCustomerMutation,
  useUpdateCustomerMutation,
  useGetCustomerInfoQuery,
  useDeleteCustomerMutation,
} = customersApiSlice;
