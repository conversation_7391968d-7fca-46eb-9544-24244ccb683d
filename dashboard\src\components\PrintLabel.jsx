import React, { useState } from 'react';
import { Box, Button, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, Typography, useTheme } from '@mui/material';
import LocalPrintshopIcon from '@mui/icons-material/LocalPrintshop';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SizeSelectionDialog from './SizeSelectionDialog';
import LuxuryButton from './LuxuryButton';
import { tokens } from '../theme';
import { generateLabelHtml } from './LabelDesign';

/**
 * Reusable PrintLabel component for inventory items
 * @param {Object} props
 * @param {Object} props.item - The inventory item data
 * @param {string} props.buttonText - Text to display on the button (default: "Print Label")
 * @param {string} props.buttonVariant - Button variant (default: "contained")
 * @param {string} props.buttonSize - Button size (default: "medium")
 * @param {boolean} props.showIcon - Whether to show the print icon (default: true)
 * @param {Object} props.buttonSx - Additional styles for the button
 * @param {boolean} props.disabled - Whether the button is disabled
 */
const PrintLabel = ({
  item,
  buttonText = "Print Label",
  buttonVariant = "contained",
  buttonSize = "medium",
  showIcon = true,
  buttonSx = {},
  disabled = false,
  showDirectPrint = false, // Option to show direct print button
}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  // State for dialogs
  const [sizeDialogOpen, setSizeDialogOpen] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [selectedSize, setSelectedSize] = useState(null);
  const [labelHtml, setLabelHtml] = useState('');

  // Handle opening the size selection dialog
  const handlePrintLabelClick = () => {
    // If the item has multiple sizes, show the dialog
    if (item?.sizes && item.sizes.length > 0) {
      setSizeDialogOpen(true);
    } else {
      // If no sizes, just print with the default size
      handlePreviewLabel();
    }
  };

  // Direct print without preview (for quick printing)
  const handleDirectPrint = () => {
    if (!item) return;

    // Generate the label HTML
    const html = generateLabelHtmlForItem();
    setLabelHtml(html);

    // Try popup method first
    const win = window.open("", "_blank", "height=400,width=800");
    if (!win) {
      // Fallback to iframe method if popup is blocked
      printWithIframe();
      return;
    }

    // Use popup method
    setTimeout(() => {
      try {
        if (!win || !win.document) {
          // Fallback to iframe if window is not available
          printWithIframe();
          return;
        }

        win.document.open();
        win.document.write(html);
        win.document.close();

        setTimeout(() => {
          try {
            win.focus();
            win.print();
            win.close();
          } catch (printError) {
            console.error("Print error:", printError);
            // Fallback to iframe method
            printWithIframe();
          }
        }, 500);
      } catch (error) {
        console.error("Window creation error:", error);
        if (win) {
          win.close();
        }
        // Fallback to iframe method
        printWithIframe();
      }
    }, 100);
  };

  // Handle preview label
  const handlePreviewLabel = (size = null) => {
    if (!item) return;

    // Generate the label HTML
    const html = generateLabelHtmlForItem(size);
    setLabelHtml(html);
    setPreviewDialogOpen(true);
  };

  // Handle size selection
  const handleSizeSelect = (size) => {
    setSelectedSize(size);
    setSizeDialogOpen(false);
    // Show preview with the selected size
    handlePreviewLabel(size);
  };

  // Use the common label design function
  const generateLabelHtmlForItem = (size = null) => {
    return generateLabelHtml(item, size);
  };

  // Alternative print method using iframe (fallback for popup blockers)
  const printWithIframe = () => {
    if (!item) return;

    // Create a hidden iframe
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.top = '-1000px';
    iframe.style.left = '-1000px';
    iframe.style.width = '1px';
    iframe.style.height = '1px';
    iframe.style.border = 'none';

    document.body.appendChild(iframe);

    // Write content to iframe
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(labelHtml);
    iframeDoc.close();

    // Print the iframe content
    setTimeout(() => {
      try {
        iframe.contentWindow.focus();
        iframe.contentWindow.print();

        // Clean up after printing
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);
      } catch (error) {
        console.error("Iframe print error:", error);
        document.body.removeChild(iframe);
        alert("Unable to print. Please try using the preview dialog.");
      }
    }, 500);
  };



  return (
    <>
      {/* Print button */}
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Tooltip title={disabled ? "Save inventory first to enable printing" : "Print label for this item"}>
          <span>
            <LuxuryButton
              onClick={handlePrintLabelClick}
              variant={buttonVariant}
              size={buttonSize}
              startIcon={showIcon ? <LocalPrintshopIcon /> : null}
              disabled={disabled || !item}
              sx={buttonSx}
            >
              {buttonText}
            </LuxuryButton>
          </span>
        </Tooltip>

        {/* Optional direct print button */}
        {showDirectPrint && (
          <Tooltip title="Print directly without preview">
            <span>
              <LuxuryButton
                onClick={handleDirectPrint}
                variant="outlined"
                size={buttonSize}
                startIcon={<LocalPrintshopIcon />}
                disabled={disabled || !item}
                sx={{ ...buttonSx, ml: 1 }}
              >
                Quick Print
              </LuxuryButton>
            </span>
          </Tooltip>
        )}
      </Box>

      {/* Size Selection Dialog */}
      <SizeSelectionDialog
        open={sizeDialogOpen}
        onClose={() => setSizeDialogOpen(false)}
        sizes={item?.sizes || []}
        onSizeSelect={handleSizeSelect}
        sku={item?.sku}
        name={item?.name}
      />

      {/* Preview Dialog */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogTitle sx={{
          fontFamily: '"Playfair Display", serif',
          color: colors.primary[500],
          borderBottom: `1px solid ${colors.primary[200]}`,
          pb: 2
        }}>
          Label Preview
        </DialogTitle>

        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Typography variant="body2" sx={{ mb: 3, color: colors.blackAccent[700] }}>
            Preview your label before printing. Click "Print" to send to printer.
          </Typography>

          <Box sx={{
            border: `1px solid ${colors.primary[200]}`,
            borderRadius: '8px',
            p: 2,
            mb: 2,
            backgroundColor: colors.whiteAccent[100],
            overflow: 'auto'
          }}>
            <iframe
              srcDoc={labelHtml}
              title="Label Preview"
              width="100%"
              height="200px"
              style={{ border: 'none' }}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setPreviewDialogOpen(false)}
            sx={{
              color: colors.blackAccent[700],
              '&:hover': {
                backgroundColor: colors.blackAccent[100],
              }
            }}
          >
            Cancel
          </Button>
          <LuxuryButton
            onClick={() => {
              // Close the dialog and print
              setPreviewDialogOpen(false);

              // Create a new window for printing
              const win = window.open("", "_blank", "height=400,width=800");
              if (!win) {
                alert("Please allow pop-ups to print labels. Check your browser's popup blocker settings.");
                return;
              }

              // Wait for the window to be ready
              setTimeout(() => {
                try {
                  // Check if the window and document are available
                  if (!win || !win.document) {
                    alert("Unable to open print window. Please check your browser settings.");
                    return;
                  }

                  // Write the HTML content to the new window
                  win.document.open();
                  win.document.write(labelHtml);
                  win.document.close();

                  // Wait a bit more for content to load, then print
                  setTimeout(() => {
                    try {
                      win.focus();
                      win.print();
                      win.close();
                    } catch (printError) {
                      console.error("Print error:", printError);
                      alert("There was an issue printing the label. Please try again.");
                    }
                  }, 500);
                } catch (error) {
                  console.error("Window creation error:", error);
                  alert("Unable to create print window. Please check your browser settings.");
                  if (win) {
                    win.close();
                  }
                }
              }, 100);
            }}
            variant="contained"
            startIcon={<LocalPrintshopIcon />}
          >
            Print
          </LuxuryButton>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PrintLabel;
