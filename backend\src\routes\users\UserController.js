const {
    models: { User, Otp, UserAddress },
} = require('../../../lib/models');
const { utcDateTime } = require('../../../lib/util');

class UserController {
    async list(req, res) {
        const { search, page = 1, limit = 10, orderby = 'desc' } = req.query;
        const finalResponse = {
            items: [],
            count: 0,
            page,
            limit,
        };
        // Convert page and limit to numbers
        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);
        let sortOrder = orderby === 'desc' ? -1 : 1;

        // Calculate the number of items to skip
        let skip = (pageNumber - 1) * limitNumber;
        try {
            let qry = { isDeleted: false, fullName: { $ne: null } };

            if (search) {
                if (search) {
                    const searchValue = new RegExp(
                        search
                            .split(' ')
                            .filter(val => val)
                            .map(value => value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'))
                            .join('|'),
                        'i'
                    );

                    qry.$or = [
                        { name: searchValue },
                        { email: searchValue },
                        { countryCode: searchValue },
                        { mobile: searchValue },
                    ];
                }
            }

            let sortCond = { created: sortOrder };

            const dataCount = await User.count(qry);

            // Fetch all questions from the database and populate the subject and classId fields
            const users = await User.find(qry)
                .select('-password')
                .sort(sortCond)
                .skip(skip)
                .limit(limitNumber)
                .exec();

            // Check if there are any questions
            if (users.length === 0) {
                return res.warn(finalResponse, req.__('NO_USERS_FOUND'));
            }

            finalResponse.items = users;
            finalResponse.count = dataCount;

            // Respond with the list of questions, including populated subject and classId fields
            res.success(finalResponse);
        } catch (error) {
            // Handle errors
            console.log('Error fetching users:', error);
            res.warn(error);
        }
    }
    async profile(req, res) {
        let { user } = req;
        let { id } = req.query;
        console.log(user)
        id &&
            (user = await User.findOne({
                _id: id,
                isDeleted: false,
            }));

        if (!user) {
            return res.notFound('', req.__('USER_NOT_EXIST'));
        }

        if (user.isSuspended) {
            return res.notFound('', req.__('YOUR_ACCOUNT_SUSPENDED'));
        }

        user = JSON.parse(JSON.stringify(user));
        ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
            key => delete user[key]
        );

        return res.success(user);
    }

    async updateProfile(req, res) {
        const { type, fullName, age, userName, bio, description, avatar, phone, email } = req.body;
        let { user } = req;

        const isUserNameExists = await User.findOne({
            _id: {
                $ne: user._id,
            },
            userName: new RegExp(`^${userName}$`, 'i'),
            isDeleted: false,
        });

        if (isUserNameExists) {
            return res.warn('', req.__('USERNAME_MATCHED'));
        }

        type === 'PROFILE_UPDATE' && fullName && (user.fullName = fullName);
        type === 'PROFILE_UPDATE' && age && (user.age = age);
        type === 'ACCOUNT_COMPLETE' && (user.isAccountComplete = true);
        type === 'PROFILE_UPDATE' && phone && (user.phone = phone);
        type === 'PROFILE_UPDATE' && email && (user.email = email);
        user.userName = userName;
        // user.bio = bio;
        // user.description = description;
        // avatar && (user.avatar = avatar);
        await user.save();

        user = JSON.parse(JSON.stringify(user));
        ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
            key => delete user[key]
        );

        return res.success(user, req.__('PROFILE_UPDATED'));
    }

    async updatePassword(req, res) {
        const { user } = req;
        const { currentPassword, newPassword } = req.body;

        const matched = await user.comparePassword(currentPassword);
        if (!matched) {
            return res.warn('', req.__('PASSWORD_MATCH_FAILURE'));
        }

        user.password = newPassword;
        await user.save();
        return res.success('', req.__('PASSWORD_CHANGED'));
    }

    async updateEmail(req, res) {
        const { user } = req;
        const { currentPassword, email } = req.body;

        const matched = await user.comparePassword(currentPassword);
        if (!matched) {
            return res.warn('', req.__('PASSWORD_MATCH_FAILURE'));
        }

        const isEmailExists = await User.findOne({
            _id: {
                $ne: user._id,
            },
            email,
            isDeleted: false,
        });

        if (isEmailExists) {
            return res.warn('', req.__('EMAIL_ALREADY_FOUND'));
        }

        user.email = email;
        await user.save();
        return res.success('', req.__('EMAIL_CHANGED'));
    }

    async updatePhone(req, res) {
        const { user } = req;
        const { currentPassword, countryCode, phone } = req.body;

        const matched = await user.comparePassword(currentPassword);
        if (!matched) {
            return res.warn('', req.__('PASSWORD_MATCH_FAILURE'));
        }

        const isPhoneExists = await User.findOne({
            _id: {
                $ne: user._id,
            },
            countryCode,
            phone,
            isDeleted: false,
        });

        if (isPhoneExists) {
            return res.warn('', req.__('PHONE_ALREADY_FOUND'));
        }

        const otp = await Otp.findOne({
            type: 'CHANGE_PHONE',
            countryCode,
            phone,
            validTill: {
                $gte: utcDateTime(),
            },
            isVerified: true,
        });

        if (!otp) {
            return res.warn('', req.__('PHONE_NOT_VERIFIED'));
        }

        user.countryCode = countryCode;
        user.phone = phone;
        await user.save();

        otp.validTill = null;
        await otp.save();

        return res.success('', req.__('PHONE_CHANGED'));
    }

    async notificationToggle(req, res) {
        const { user } = req;
        user.pushNotificationAllowed = !user.pushNotificationAllowed;
        await user.save();

        return res.success(
            {
                pushNotificationAllowed: user.pushNotificationAllowed,
            },
            user.pushNotificationAllowed ? req.__('NOTIFICATION_TURNED_ON') : req.__('NOTIFICATION_TURNED_OFF')
        );
    }

    async addAddress(req, res) {
        const userId = req.user._id; // Assuming userId is available in req.user
        const {
            addressLine1,
            addressLine2,
            city,
            state,
            postalCode,
            country,
            phone,
            email,
            fName,
            lName,
            cName,
            nickName,
            fullName,
        } = req.body;

        try {
            // Check if user exists
            const user = await User.findById(userId);
            if (!user) {
                return res.warn({}, req.__('NO_USERS_FOUND'));
            }

            // Create a new address
            const newAddress = new UserAddress({
                userId,
                addressLine1,
                addressLine2,
                city,
                state,
                postalCode,
                country,
                phone,
                email,
                fName,
                lName,
                cName,
                nickName,
                fullName,
            });

            const savedAddress = await newAddress.save();
            return res.success(savedAddress, req.__('Address add successfully'));
        } catch (error) {
            console.error('Error while adding address:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async editAddress(req, res) {
        const userId = req.user._id;
        const addressId = req.params.addressId;
        const {
            addressLine1,
            addressLine2,
            city,
            state,
            postalCode,
            country,
            phone,
            email,
            fName,
            lName,
            cName,
            nickName,
            fullName,
        } = req.body;

        try {
            // Check if user exists
            const user = await User.findById(userId);
            if (!user) {
                return res.warn({}, req.__('NO_USERS_FOUND'));
            }

            // Find the address and update it
            const updatedAddress = await UserAddress.findOneAndUpdate(
                { _id: addressId, userId }, // Find the address by ID and user ID
                {
                    addressLine1,
                    addressLine2,
                    city,
                    state,
                    postalCode,
                    country,
                    phone,
                    email,
                    fName,
                    lName,
                    cName,
                    nickName,
                    fullName,
                },
                { new: true } // Return the updated document
            );

            if (!updatedAddress) {
                return res.warn({}, req.__('ADDRESS_NOT_FOUND'));
            }

            return res.success(updatedAddress, req.__('Address updated successfully'));
        } catch (error) {
            console.error('Error while updating address:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async listAddresses(req, res) {
        const userId = req.user._id; // Assuming userId is available in req.user

        try {
            // Find all addresses for the user
            const addresses = await UserAddress.find({ userId });

            if (!addresses || addresses.length === 0) {
                return res.warn({}, req.__('NO_ADDRESSES_FOUND'));
            }

            return res.success(addresses, req.__('Addresses retrieved successfully'));
        } catch (error) {
            console.error('Error while retrieving addresses:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getAddressDetail(req, res) {
        const userId = req.user._id; // Assuming userId is available in req.user
        const addressId = req.params.addressId; // Address ID from URL parameters

        try {
            // Check if user exists
            const user = await User.findById(userId);
            if (!user) {
                return res.warn({}, req.__('NO_USERS_FOUND'));
            }

            // Find the address by ID and user ID
            const address = await UserAddress.findOne({ _id: addressId, userId });

            if (!address) {
                return res.warn({}, req.__('ADDRESS_NOT_FOUND'));
            }

            return res.success(address, req.__('Address details retrieved successfully'));
        } catch (error) {
            console.error('Error while retrieving address details:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new UserController();
