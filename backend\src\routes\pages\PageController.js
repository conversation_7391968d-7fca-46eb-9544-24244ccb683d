const {
    models: { Page },
} = require('../../../lib/models');

class PageController {
    async page(req, res) {
        const page = await Page.findOne({
            slug: req.params.slug,
            isSuspended: false,
        });

        if (!page) {
            return res.warn('', req.__('PAGE_NOT_EXISTS'));
        }

        return res.success(page);
    }

    async addPage(req, res) {
        const { title, description, slug } = req.body;

        try {
            // Create a new page instance with data from request body
            const newPage = new Page({
                title,
                description,
                slug,
            });

            // Save the new page to the database
            const savedPage = await newPage.save();

            // Return a success response with the added page data
            return res.success(savedPage, req.__('PAGE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            // Handle errors and return appropriate error response
            console.error('Error adding page:', error);
            return res.warn('', req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async editPage(req, res) {
        const { slug } = req.params; // Extract pageId from request parameters
        const { title, description } = req.body; // Extract updated data from request body

        try {
            // Find the page by its ID
            const page = await Page.findOne({ slug });

            // If page is not found, return a not found response
            if (!page) {
                return res.warn({}, req.__('PAGE_NOT_FOUND'));
            }

            // Update the page fields
            if (title) {
                page.title = title;
            }
            if (description) {
                page.description = description;
            }
            if (slug) {
                page.slug = slug;
            }

            // Save the updated page to the database
            const updatedPage = await page.save();

            // Return a success response with the updated page data
            return res.success(updatedPage, req.__('PAGE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            // Handle errors and return appropriate error response
            console.error('Error updating page:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async listPages(req, res) {
        try {
            const { search } = req.query;

            // Base query object
            let query = {
                isSuspended: false,
            };

            // Add search criteria if provided
            if (search) {
                // Create a case-insensitive regular expression for the search term
                const searchRegex = new RegExp(search, 'i');
                query.$or = [{ title: searchRegex }, { slug: searchRegex }];
            }

            // Find pages based on the query
            const pages = await Page.find(query);

            return res.success(pages, req.__('PAGES_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error retrieving pages:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new PageController();
