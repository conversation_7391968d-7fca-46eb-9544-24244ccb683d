const sgMail = require('@sendgrid/mail');
const fs = require('fs');
const path = require('path');
const ejs = require('ejs');

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

const sendMail = async (template, subject, to, data = {}) => {
    try {
        const templatePath = path.join(__dirname, '..', 'views', 'emails', `${template}.ejs`);
        const templateContent = fs.readFileSync(templatePath, 'utf8');
        const html = ejs.render(templateContent, data);

        const msg = {
            to,
            from: process.env.FROM_MAIL,
            subject,
            html,
        };

        await sgMail.send(msg);
        return true;
    } catch (error) {
        console.error('Error sending email:', error);
        return false;
    }
};

module.exports = {
    sendMail,
}; 