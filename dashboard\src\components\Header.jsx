import { Typography, Box, useTheme, Divider } from "@mui/material";
import { tokens } from "../theme";

const Header = ({ title, subtitle }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Box mb="30px" className="luxury-header">
      <Typography
        variant="h2"
        color={colors.primary[500]} // Antique gold color
        fontWeight={500}
        sx={{
          m: "0 0 5px 0",
          textTransform: "capitalize",
          letterSpacing: "0.5px",
          position: "relative",
          display: "inline-block",
          paddingBottom: "8px",
          "&::after": {
            content: '""',
            position: "absolute",
            width: "40%",
            height: "2px",
            bottom: 0,
            left: 0,
            backgroundColor: colors.primary[500], // Antique gold underline
          }
        }}
      >
        {title}
      </Typography>
      <Typography
        variant="h5"
        color={colors.blackAccent[700]} // Charcoal color
        sx={{ mt: 1 }}
      >
        {subtitle}
      </Typography>
    </Box>
  );
};

export default Header;
