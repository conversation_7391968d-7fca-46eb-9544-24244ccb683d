require('dotenv').config();
const mongoose = require('mongoose');
const { models: { Inventory } } = require('../../lib/models');

const sampleInventory = [
    {
      "name": "Diamond Solitaire Ring",
      "category": "diamond",
      "description": "1 carat round brilliant diamond set in 18K white gold",
      "type": "Ring",
      "size": "6",
      "purity": "18",
      "weight": {
        "netWt": { "value": 4.2, "unit": "g" },
        "grossWt": { "value": 4.5, "unit": "g" },
        "stoneWt": { "value": 1.0, "unit": "ct" },
        "diamondWt": { "value": 1.0, "unit": "ct" }
      },
      "price": {
        "costPrice": 150000,
        "sellingPrice": 225000,
        "mrp": 250000,
        "makingCharges": 15000
      },
      "stock": {
        "quantity": 2,
        "minStockLevel": 1
      },
      "images": ["https://example.com/diamond-ring.jpg"],
      "hasGST": true,
      "gstRate": 3,
      "tags": ["engagement", "solitaire", "diamond"],
      "status": "available"
    },
    {
      "name": "Gold Chain Necklace",
      "category": "gold",
      "description": "22K gold chain with traditional design",
      "type": "Necklace",
      "purity": "22",
      "weight": {
        "netWt": { "value": 25.5, "unit": "g" },
        "grossWt": { "value": 25.5, "unit": "g" }
      },
      "price": {
        "costPrice": 120000,
        "sellingPrice": 145000,
        "mrp": 160000,
        "makingCharges": 12000
      },
      "stock": {
        "quantity": 3,
        "minStockLevel": 1
      },
      "images": ["https://example.com/gold-chain.jpg"],
      "tags": ["chain", "traditional", "wedding"],
      "status": "available"
    },
    {
      "name": "Pearl and Diamond Earrings",
      "category": "pearl",
      "description": "South Sea pearls with diamond accents in 18K white gold",
      "type": "Earrings",
      "purity": "18",
      "weight": {
        "netWt": { "value": 8.3, "unit": "g" },
        "grossWt": { "value": 8.5, "unit": "g" },
        "diamondWt": { "value": 0.5, "unit": "ct" }
      },
      "price": {
        "costPrice": 85000,
        "sellingPrice": 125000,
        "mrp": 140000,
        "makingCharges": 8000
      },
      "stock": {
        "quantity": 4,
        "minStockLevel": 2
      },
      "images": ["https://example.com/pearl-earrings.jpg"],
      "tags": ["pearl", "elegant", "wedding"],
      "status": "available"
    },
    {
      "name": "Platinum Wedding Band",
      "category": "platinum",
      "description": "950 Platinum plain wedding band with comfort fit",
      "type": "Ring",
      "size": "10",
      "purity": "22",
      "weight": {
        "netWt": { "value": 6.0, "unit": "g" },
        "grossWt": { "value": 6.0, "unit": "g" }
      },
      "price": {
        "costPrice": 45000,
        "sellingPrice": 65000,
        "mrp": 75000,
        "makingCharges": 5000
      },
      "stock": {
        "quantity": 5,
        "minStockLevel": 2
      },
      "images": ["https://example.com/platinum-band.jpg"],
      "tags": ["wedding", "men", "platinum"],
      "status": "available"
    },
    {
      "name": "Ruby and Diamond Bracelet",
      "category": "gemstone",
      "description": "Burma rubies with diamond halo in 18K rose gold",
      "type": "Bracelet",
      "purity": "18",
      "weight": {
        "netWt": { "value": 15.7, "unit": "g" },
        "grossWt": { "value": 16.0, "unit": "g" },
        "stoneWt": { "value": 3.5, "unit": "ct" },
        "diamondWt": { "value": 1.2, "unit": "ct" }
      },
      "price": {
        "costPrice": 180000,
        "sellingPrice": 250000,
        "mrp": 275000,
        "makingCharges": 20000
      },
      "stock": {
        "quantity": 2,
        "minStockLevel": 1
      },
      "images": ["https://example.com/ruby-bracelet.jpg"],
      "tags": ["ruby", "luxury", "bracelet"],
      "status": "available"
    },
    {
      "name": "Silver Anklet",
      "category": "silver",
      "description": "Traditional silver anklet with bells",
      "type": "Anklet",
      "purity": "ss",
      "weight": {
        "netWt": { "value": 28.5, "unit": "g" },
        "grossWt": { "value": 28.5, "unit": "g" }
      },
      "price": {
        "costPrice": 2500,
        "sellingPrice": 3500,
        "mrp": 4000,
        "makingCharges": 500
      },
      "stock": {
        "quantity": 8,
        "minStockLevel": 3
      },
      "images": ["https://example.com/silver-anklet.jpg"],
      "tags": ["traditional", "silver", "anklet"],
      "status": "available"
    },
    {
      "name": "Diamond Tennis Bracelet",
      "category": "diamond",
      "description": "5 carat total weight diamond tennis bracelet in 18K white gold",
      "type": "Bracelet",
      "purity": "18",
      "weight": {
        "netWt": { "value": 18.3, "unit": "g" },
        "grossWt": { "value": 18.5, "unit": "g" },
        "diamondWt": { "value": 5.0, "unit": "ct" }
      },
      "price": {
        "costPrice": 350000,
        "sellingPrice": 475000,
        "mrp": 500000,
        "makingCharges": 25000
      },
      "stock": {
        "quantity": 1,
        "minStockLevel": 1
      },
      "images": ["https://example.com/tennis-bracelet.jpg"],
      "tags": ["diamond", "luxury", "bracelet"],
      "status": "available"
    },
    {
      "name": "Gold Mangalsutra",
      "category": "gold",
      "description": "Traditional mangalsutra in 22K gold with black beads",
      "type": "Necklace",
      "purity": "22",
      "weight": {
        "netWt": { "value": 15.8, "unit": "g" },
        "grossWt": { "value": 16.0, "unit": "g" }
      },
      "price": {
        "costPrice": 85000,
        "sellingPrice": 98000,
        "mrp": 105000,
        "makingCharges": 8000
      },
      "stock": {
        "quantity": 4,
        "minStockLevel": 2
      },
      "images": ["https://example.com/mangalsutra.jpg"],
      "tags": ["traditional", "wedding", "religious"],
      "status": "available"
    },
    {
      "name": "Emerald Ring",
      "category": "gemstone",
      "description": "Colombian emerald with diamond accent in 18K yellow gold",
      "type": "Ring",
      "size": "7",
      "purity": "18",
      "weight": {
        "netWt": { "value": 5.2, "unit": "g" },
        "grossWt": { "value": 5.4, "unit": "g" },
        "stoneWt": { "value": 1.5, "unit": "ct" },
        "diamondWt": { "value": 0.3, "unit": "ct" }
      },
      "price": {
        "costPrice": 125000,
        "sellingPrice": 175000,
        "mrp": 190000,
        "makingCharges": 15000
      },
      "stock": {
        "quantity": 2,
        "minStockLevel": 1
      },
      "images": ["https://example.com/emerald-ring.jpg"],
      "tags": ["emerald", "cocktail", "luxury"],
      "status": "available"
    },
    {
      "name": "Pearl Necklace Set",
      "category": "pearl",
      "description": "Double-strand cultured pearl necklace with matching earrings",
      "type": "Necklace Set",
      "purity": "18",
      "weight": {
        "netWt": { "value": 45.0, "unit": "g" },
        "grossWt": { "value": 45.5, "unit": "g" }
      },
      "price": {
        "costPrice": 75000,
        "sellingPrice": 95000,
        "mrp": 110000,
        "makingCharges": 10000
      },
      "stock": {
        "quantity": 3,
        "minStockLevel": 1
      },
      "images": ["https://example.com/pearl-set.jpg"],
      "tags": ["pearl", "set", "wedding"],
      "status": "available"
    }
  ]

async function seedInventory() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        // Clear existing inventory
        await Inventory.deleteMany({});
        console.log('Cleared existing inventory');

        // Add admin user ID (you should replace this with a valid admin ID from your database)
        const adminUserId = '64f8e7c4f3c48c7c9b0485cd'; // Replace with actual admin ID
        const inventoryWithAdmin = sampleInventory.map(item => ({
            ...item,
            createdBy: adminUserId
        }));

        // Insert new inventory
        const result = await Inventory.insertMany(inventoryWithAdmin);
        console.log(`Successfully added ${result.length} inventory items`);

        console.log('Sample inventory data has been seeded!');
    } catch (error) {
        console.error('Error seeding inventory:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }
}

seedInventory();