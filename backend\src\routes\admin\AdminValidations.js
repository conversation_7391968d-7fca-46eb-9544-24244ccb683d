const { Joi, common } = require('../../util/validations');

const logIn = Joi.object({
    email: common.email,
    password: Joi.string().required().messages({
        'any.required': 'Password is required'
    })
});

const updatePassword = Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: common.adminPassword,
    confirmPassword: Joi.string()
        .valid(Joi.ref('newPassword'))
        .required()
        .messages({
            'any.only': 'Passwords must match',
            'any.required': 'Confirm password is required'
        })
});

const forgotPassword = Joi.object({
    email: common.email
});

const resetPassword = Joi.object({
    otp: Joi.string().required(),
    newPassword: common.adminPassword
});

module.exports = {
    logIn,
    updatePassword,
    forgotPassword,
    resetPassword
};
