import React from "react";
import {
  Typography,
  useTheme,
  useMediaQuery,
  Box,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  Paper,
  Container
} from "@mui/material";
import parse from "html-react-parser";
import { useSearchParams } from "react-router-dom";
import { useGetPublicInventoriesQuery } from "services";
import { tokens } from "../../theme";

// Icons
import DiamondIcon from '@mui/icons-material/Diamond';
import BalanceIcon from '@mui/icons-material/Balance';
import PaidIcon from '@mui/icons-material/Paid';
import InventoryIcon from '@mui/icons-material/Inventory';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import InfoIcon from '@mui/icons-material/Info';

const InventoryBannerView = () => {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("itemId");
  const { data: item } = useGetPublicInventoriesQuery(
    { id },
    { skip: !id, refetchOnMountOrArgChange: true }
  );
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));

  // Custom styles for the luxury view
  const viewStyles = {
    container: {
      maxWidth: '1200px',
      margin: '24px auto',
      padding: '0',
    },
    card: {
      borderRadius: '12px',
      boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.08)',
      overflow: 'hidden',
      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
      height: '100%',
      '&:hover': {
        transform: 'translateY(-5px)',
        boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.12)',
      },
    },
    header: {
      backgroundColor: colors.primary[500], // Antique gold
      color: colors.whiteAccent[100],
      padding: '16px 24px',
      fontFamily: '"Playfair Display", serif',
      fontSize: '1.5rem',
      fontWeight: 600,
    },
    sectionTitle: {
      fontFamily: '"Playfair Display", serif',
      color: colors.primary[500],
      fontWeight: 500,
      mb: 2,
      position: 'relative',
      display: 'inline-block',
      paddingBottom: '8px',
      '&::after': {
        content: '""',
        position: 'absolute',
        width: '40%',
        height: '2px',
        bottom: 0,
        left: 0,
        backgroundColor: colors.primary[500],
      },
    },
    divider: {
      my: 2,
      background: `linear-gradient(to right, transparent, ${colors.primary[400]}, transparent)`,
      opacity: 0.5,
    },
    infoLabel: {
      color: colors.blackAccent[700],
      fontWeight: 500,
      fontSize: '0.9rem',
      mb: 0.5,
    },
    infoValue: {
      color: colors.blackAccent[900],
      fontWeight: 400,
      fontSize: '1rem',
      mb: 2,
    },
    imageContainer: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      mt: 3,
      mb: 3,
      height: '200px',
      backgroundColor: colors.whiteAccent[200],
      borderRadius: '8px',
      overflow: 'hidden',
    },
    sectionIcon: {
      color: colors.primary[500],
      mr: 1,
      fontSize: '1.5rem',
    },
    sectionHeader: {
      display: 'flex',
      alignItems: 'center',
      mb: 2,
    },
    chip: {
      backgroundColor: colors.primary[100],
      color: colors.primary[700],
      fontWeight: 500,
      m: 0.5,
    },
  };

  // Format values with proper capitalization and handling nulls
  const formatValue = (value) => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'string') return value.charAt(0).toUpperCase() + value.slice(1);
    return value;
  };

  if (!item) return null;

  return (
    <Container sx={viewStyles.container}>
      {/* Header */}
      {/* <Box sx={viewStyles.header}>
        {item.name} &nbsp;
        <Typography component="span" variant="subtitle2" sx={{ fontFamily: '"Inter", sans-serif' }}>
          (SKU: {item.sku} | Category: {formatValue(item.category)})
        </Typography>
      </Box> */}

      <Grid container spacing={3} sx={{ p: 3, backgroundColor: colors.whiteAccent[100] }}>
        {/* LEFT COLUMN - Basic Info and Image */}
        <Grid item xs={12} md={4}>
          <Card sx={viewStyles.card}>
            <CardContent>
              <Box sx={viewStyles.sectionHeader}>
                <DiamondIcon sx={viewStyles.sectionIcon} />
                <Typography variant="h4" sx={viewStyles.sectionTitle}>
                  Basic Information
                </Typography>
              </Box>
              <Divider sx={viewStyles.divider} />

              {/* Item Image Placeholder */}
              <Box sx={viewStyles.imageContainer}>
                {item?.images?.length > 0 ? (
                  <img
                    src={item.images[0]}
                    alt={item?.name || 'Jewelry item'}
                    style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                  />
                ) : (
                  <DiamondIcon sx={{ fontSize: 80, color: colors.primary[300] }} />
                )}
              </Box>

              {/* Basic Info Fields */}
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="h5" sx={{ fontWeight: 600, color: colors.primary[500], mb: 1 }}>
                    {formatValue(item?.name)}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>SKU</Typography>
                  <Typography sx={viewStyles.infoValue}>{formatValue(item?.sku)}</Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>Category</Typography>
                  <Typography sx={viewStyles.infoValue}>{formatValue(item?.category)}</Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>Type</Typography>
                  <Typography sx={viewStyles.infoValue}>{formatValue(item?.type)}</Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography sx={viewStyles.infoLabel}>Purity</Typography>
                  <Typography sx={viewStyles.infoValue}>{formatValue(item?.purity)}</Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography sx={viewStyles.infoLabel}>Status</Typography>
                  <Chip
                    label={formatValue(item?.status)}
                    sx={{
                      ...viewStyles.chip,
                      backgroundColor: item?.status === 'available' ? colors.greenAccent[100] : colors.primary[100],
                      color: item?.status === 'available' ? colors.greenAccent[700] : colors.primary[700],
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* RIGHT COLUMN - Detailed Information */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* Weight & Measurements Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <BalanceIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Weight & Measurements
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Net Weight</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.weight?.netWt?.value ?
                          `${item.weight.netWt.value} ${item.weight.netWt.unit || 'g'}` : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Gross Weight</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.weight?.grossWt?.value ?
                          `${item.weight.grossWt.value} ${item.weight.grossWt.unit || 'g'}` : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Stone Weight</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.weight?.stoneWt?.value ?
                          `${item.weight.stoneWt.value} ${item.weight.stoneWt.unit || 'ct'}` : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Diamond Weight</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.weight?.diamondWt?.value ?
                          `${item.weight.diamondWt.value} ${item.weight.diamondWt.unit || 'ct'}` : 'N/A'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Pricing Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <PaidIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Pricing Details
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Cost Price</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.price?.costPrice ? `₹${item.price.costPrice.toLocaleString()}` : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Selling Price</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.price?.sellingPrice ? `₹${item.price.sellingPrice.toLocaleString()}` : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>MRP</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.price?.mrp ? `₹${item.price.mrp.toLocaleString()}` : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Making Charges</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.price?.makingCharges ? `₹${item.price.makingCharges.toLocaleString()}` : 'N/A'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Inventory Management Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <InventoryIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Inventory Management
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Quantity</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.stock?.quantity !== undefined ? item.stock.quantity : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Min Stock Level</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.stock?.minStockLevel !== undefined ? item.stock.minStockLevel : 'N/A'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Typography sx={viewStyles.infoLabel}>Stock Status</Typography>
                      <Chip
                        label={item?.isLowStock ? "Low Stock" : "In Stock"}
                        sx={{
                          ...viewStyles.chip,
                          backgroundColor: item?.isLowStock ? colors.redAccent[100] : colors.greenAccent[100],
                          color: item?.isLowStock ? colors.redAccent[700] : colors.greenAccent[700],
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Tags & Additional Info Section */}
            <Grid item xs={12} md={6}>
              <Card sx={viewStyles.card}>
                <CardContent>
                  <Box sx={viewStyles.sectionHeader}>
                    <LocalOfferIcon sx={viewStyles.sectionIcon} />
                    <Typography variant="h4" sx={viewStyles.sectionTitle}>
                      Tags & Additional Info
                    </Typography>
                  </Box>
                  <Divider sx={viewStyles.divider} />

                  {/* Tags */}
                  <Typography sx={viewStyles.infoLabel}>Tags</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', mb: 2 }}>
                    {item?.tags?.length > 0 ? (
                      item.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={formatValue(tag)}
                          sx={viewStyles.chip}
                        />
                      ))
                    ) : (
                      <Typography sx={{ color: colors.blackAccent[500], fontStyle: 'italic' }}>No tags</Typography>
                    )}
                  </Box>

                  {/* GST Info */}
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography sx={viewStyles.infoLabel}>Has GST</Typography>
                      <Typography sx={viewStyles.infoValue}>
                        {item?.hasGST ? "Yes" : "No"}
                      </Typography>
                    </Grid>

                    {item?.hasGST && (
                      <Grid item xs={6}>
                        <Typography sx={viewStyles.infoLabel}>GST Rate</Typography>
                        <Typography sx={viewStyles.infoValue}>
                          {item?.gstRate ? `${item.gstRate}%` : 'N/A'}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Description Section */}
            {item?.description && (
              <Grid item xs={12}>
                <Card sx={viewStyles.card}>
                  <CardContent>
                    <Box sx={viewStyles.sectionHeader}>
                      <InfoIcon sx={viewStyles.sectionIcon} />
                      <Typography variant="h4" sx={viewStyles.sectionTitle}>
                        Description
                      </Typography>
                    </Box>
                    <Divider sx={viewStyles.divider} />

                    <Box sx={{ p: 2, backgroundColor: colors.whiteAccent[200], borderRadius: '8px' }}>
                      {parse(item?.description ?? "")}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>
    </Container>
  );
};

// We no longer need the Detail component as we've created a more sophisticated layout

export default InventoryBannerView;
