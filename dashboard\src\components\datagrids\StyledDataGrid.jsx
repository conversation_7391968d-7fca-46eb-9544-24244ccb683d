import React from "react";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import styled from "@emotion/styled";
import dataGridStyles from "../../styles/dataGridStyles";

const Container = styled(Box)`
  height: ${({ isMobile }) => isMobile ? '60vh' : '70vh'};
  width: 100%;
  padding: ${({ theme, isMobile }) => isMobile ? theme.spacing(1) : theme.spacing(2)};
  box-sizing: border-box;
  background-color: ${({ theme }) => theme.palette.background.paper};
  border-radius: ${({ theme }) => theme.shape.borderRadius};
  box-shadow: ${({ theme }) => theme.shadows[2]};
  border: 1px solid ${({ theme }) => theme.palette.divider}; /* Adds a subtle border to unify the look */
  overflow: hidden;
`;

const StyledDataGrid = ({
  rows,
  columns,
  getRowId,
  loading,
  pagination,
  page,
  pageSize,
  rowCount,
  paginationMode,
  onPageChange,
  onPageSizeChange,
  noResultText = "No results found.",
  customStyle = {},
  containerStyle = {},
  enableFiltering = true,
  enableColumnVisibilityToggle = true,
  enableDensitySelector = true,
  enableExport = true,
  ...rest
}) => {
  const theme = useTheme();
  const styles = dataGridStyles(theme.palette.mode);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Adjust columns for mobile view
  const responsiveColumns = React.useMemo(() => {
    if (isMobile) {
      // On mobile, prioritize important columns and reduce flex values
      return columns.map(col => ({
        ...col,
        flex: col.flex ? 0.5 : undefined, // Reduce flex values
        minWidth: col.minWidth || 100, // Ensure minimum width
        // Hide less important columns on mobile if needed
        // This is optional and depends on your specific requirements
      }));
    }
    return columns;
  }, [columns, isMobile]);

  return (
    <Container style={containerStyle} isMobile={isMobile}>
      <DataGrid
        sx={{
          ...styles.listContainer,
          border: "none", // Removes default borders
          borderRadius: 1, // Ensures corners are rounded
          boxShadow: "none", // Removes default shadows
          '& .MuiDataGrid-cell': {
            fontSize: isMobile ? '0.75rem' : '0.875rem',
            padding: isMobile ? '8px 4px' : '16px 8px',
          },
          '& .MuiDataGrid-columnHeaders': {
            fontSize: isMobile ? '0.75rem' : '0.875rem',
            padding: isMobile ? '4px 2px' : '8px 4px',
          },
          ...customStyle, // Apply custom styles
        }}
        rows={rows}
        columns={responsiveColumns}
        getRowId={getRowId}
        loading={loading}
        pagination={pagination}
        page={page}
        pageSize={pageSize}
        rowCount={rowCount}
        paginationMode={paginationMode}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        {...rest} // Forward any remaining props to DataGrid
        disableColumnFilter={!enableFiltering}
        disableColumnSelector={!enableColumnVisibilityToggle}
        disableDensitySelector={!enableDensitySelector}
        components={{
          Toolbar: GridToolbar,
        }}
        componentsProps={{
          toolbar: {
            showQuickFilter: true,
            quickFilterProps: { debounceMs: 500 },
            printOptions: { disableToolbarButton: !enableExport },
            csvOptions: { disableToolbarButton: !enableExport },
          },
        }}
        localeText={{
          noRowsLabel: noResultText,
          toolbarDensity: 'Size',
          toolbarDensityLabel: 'Size',
          toolbarDensityCompact: 'Small',
          toolbarDensityStandard: 'Medium',
          toolbarDensityComfortable: 'Large',
        }}
      />
    </Container>
  );
};

export default StyledDataGrid;
