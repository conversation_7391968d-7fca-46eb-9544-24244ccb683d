const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const enums = {
    Platform: {
        ANDROID: 'ANDROID',
        IOS: 'IOS',
        WEB: 'WEB'
    }
};

const adminSchema = new mongoose.Schema({
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    countryCode: { type: String },
    contactNumber: { type: String },
    isSuspended: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
    failedLoginAttempts: { type: Number, default: 0 },
    preventLoginTill: { type: Number, default: 0 },
    authTokenIssuedAt: { type: Number, default: 0 },
}, { timestamps: true });

adminSchema.pre('save', async function(next) {
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 10);
    }
    next();
});

adminSchema.methods.comparePassword = async function(password) {
    return bcrypt.compare(password, this.password);
};

const adminSettingsSchema = new mongoose.Schema({
    androidAppVersion: { type: String, default: '1.0.0' },
    androidForceUpdate: { type: Boolean, default: false },
    iosAppVersion: { type: String, default: '1.0.0' },
    iosForceUpdate: { type: Boolean, default: false },
    webAppVersion: { type: String, default: '1.0.0' },
    webForceUpdate: { type: Boolean, default: false },
    maintenance: { type: Boolean, default: false },
}, { timestamps: true });

const userSchema = new mongoose.Schema({
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    countryCode: { type: String },
    contactNumber: { type: String },
    isSuspended: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
}, { timestamps: true });

const models = {
    Admin: mongoose.model('Admin', adminSchema),
    AdminSettings: mongoose.model('AdminSettings', adminSettingsSchema),
    User: mongoose.model('User', userSchema),
    Product: mongoose.model('Product', new mongoose.Schema({}, { strict: false })),
    SizeType: mongoose.model('SizeType', new mongoose.Schema({}, { strict: false })),
    CaptionTag: mongoose.model('CaptionTag', new mongoose.Schema({}, { strict: false })),
    ClothLength: mongoose.model('ClothLength', new mongoose.Schema({}, { strict: false })),
    Color: mongoose.model('Color', new mongoose.Schema({}, { strict: false })),
    Discount: mongoose.model('Discount', new mongoose.Schema({}, { strict: false })),
    Fabric: mongoose.model('Fabric', new mongoose.Schema({}, { strict: false })),
    Sections: mongoose.model('Sections', new mongoose.Schema({}, { strict: false })),
    Occasion: mongoose.model('Occasion', new mongoose.Schema({}, { strict: false })),
    Pattern: mongoose.model('Pattern', new mongoose.Schema({}, { strict: false })),
    PaymentMode: mongoose.model('PaymentMode', new mongoose.Schema({}, { strict: false })),
    Brand: mongoose.model('Brand', new mongoose.Schema({}, { strict: false })),
    Category: mongoose.model('Category', new mongoose.Schema({}, { strict: false })),
};

module.exports = {
    enums,
    models
}; 