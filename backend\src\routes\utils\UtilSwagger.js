const utilSchema = {
    type: 'object',
    properties: {
        url: {
            type: 'string',
            description: 'URL of the uploaded file'
        },
        originalName: {
            type: 'string',
            description: 'Original name of the uploaded file'
        },
        mimetype: {
            type: 'string',
            description: 'MIME type of the file'
        },
        size: {
            type: 'number',
            description: 'Size of the file in bytes'
        }
    }
};

const dashboardSchema = {
    type: 'object',
    properties: {
        ordersCount: {
            type: 'number',
            description: 'Total number of orders'
        },
        usersCount: {
            type: 'number',
            description: 'Total number of users'
        },
        productsCount: {
            type: 'number',
            description: 'Total number of products'
        },
        todaysCollection: {
            type: 'number',
            description: 'Total collection amount for today'
        },
        pendingOrders: {
            type: 'array',
            items: {
                type: 'object'
            },
            description: 'List of pending orders'
        },
        cancelOrders: {
            type: 'array',
            items: {
                type: 'object'
            },
            description: 'List of cancelled orders'
        }
    }
};

const utilSwagger = {
    tags: [
        {
            name: 'Utils',
            description: 'Utility operations including file uploads and dashboard'
        }
    ],
    paths: {
        '/api/utils/upload-file': {
            get: {
                tags: ['Utils'],
                summary: 'Get signed URL for file upload',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' },
                    {
                        name: 'location',
                        in: 'query',
                        required: true,
                        schema: {
                            type: 'string'
                        },
                        description: 'Upload location path'
                    },
                    {
                        name: 'type',
                        in: 'query',
                        required: true,
                        schema: {
                            type: 'string',
                            enum: ['IMAGE', 'DOCUMENT.PDF']
                        },
                        description: 'Type of file to upload'
                    },
                    {
                        name: 'count',
                        in: 'query',
                        required: true,
                        schema: {
                            type: 'string',
                            pattern: '^[0-9]+$'
                        },
                        description: 'Number of URLs to generate'
                    }
                ],
                responses: {
                    200: {
                        description: 'Signed URLs generated successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: {
                                        type: 'string'
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/utils/upload-media': {
            post: {
                tags: ['Utils'],
                summary: 'Upload media file',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'multipart/form-data': {
                            schema: {
                                type: 'object',
                                required: ['media', 'type', 'folder'],
                                properties: {
                                    media: {
                                        type: 'string',
                                        format: 'binary',
                                        description: 'Media file to upload'
                                    },
                                    type: {
                                        type: 'string',
                                        enum: ['image', 'video'],
                                        description: 'Type of media'
                                    },
                                    folder: {
                                        type: 'string',
                                        description: 'Destination folder'
                                    }
                                }
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Media uploaded successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/UploadResponse'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/api/utils/contact-us': {
            post: {
                tags: ['Utils'],
                summary: 'Submit contact form',
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                required: ['name', 'email', 'message'],
                                properties: {
                                    name: {
                                        type: 'string',
                                        description: 'Name of the contact'
                                    },
                                    email: {
                                        type: 'string',
                                        format: 'email',
                                        description: 'Email address'
                                    },
                                    message: {
                                        type: 'string',
                                        description: 'Contact message'
                                    }
                                }
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Contact form submitted successfully'
                    }
                }
            }
        },
        '/api/utils/dashboard': {
            get: {
                tags: ['Utils'],
                summary: 'Get dashboard information',
                security: [{ bearerAuth: [] }],
                parameters: [
                    { $ref: '#/components/parameters/headerLanguage' },
                    { $ref: '#/components/parameters/headerPlatform' },
                    { $ref: '#/components/parameters/headerVersion' }
                ],
                responses: {
                    200: {
                        description: 'Dashboard information retrieved successfully',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/DashboardResponse'
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    components: {
        schemas: {
            UploadResponse: utilSchema,
            DashboardResponse: dashboardSchema
        }
    }
};

module.exports = utilSwagger;